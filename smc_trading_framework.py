import MetaTrader5 as mt5
import json
import time
from datetime import datetime
from xai_sdk.v2 import Client

# Configuration and Prompts
GROK_API_KEY = "************************************************************************************"  # Your Grok API key
GROK_API_URL = "api.x.ai"  # Updated based on x.ai documentation
SYMBOLS = ["EURUSDm"]  # Testing with EURUSDm
TIMEFRAME_15M = mt5.TIMEFRAME_M15  # 15-minute timeframe
TIMEFRAME_4H = mt5.TIMEFRAME_H4   # 4-hour timeframe
CONFIDENCE_THRESHOLD = 80  # Minimum confidence for signals
SIGNAL_FILE = "smc_signals.txt"  # File to log signals for manual review

ORDER_BLOCK_PROMPT = """
You are an expert in Smart Money Concepts (SMC) trading. Analyze the provided price data (OHLC: Open, High, Low, Close) for the specified instrument over the given timeframe. Identify potential Order Blocks, which are the last significant bullish or bearish candle before a major price move, typically where institutional buying or selling occurs. A bullish Order Block is the last bullish candle before a strong upward move, often at a support level. A bearish Order Block is the last bearish candle before a strong downward move, often at a resistance level. Use the OHLC data to determine timestamps and price ranges. Provide the following:

1. Timestamp and price range (high/low) of the detected Order Block.
2. Type (bullish or bearish).
3. Confidence score (0-100%) based on candle size, volume (if available), and proximity to key support/resistance levels.
4. Recommended action: Wait for price to retest the Order Block for a potential entry (buy for bullish, sell for bearish).

**Input Data**:
- Instrument: {symbol}
- Timeframe: {timeframe}
- OHLC Data: {ohlc_data}
- Support/Resistance Levels: {sr_levels}

**Output Format**:
- Order Block: {"timestamp": "YYYY-MM-DDTHH:MM:SS", "type": "bullish/bearish", "price_range": {"high": float, "low": float}, "confidence": int, "action": "string"}
"""

FVG_PROMPT = """
You are an expert in Smart Money Concepts (SMC) trading. Analyze the provided price data (OHLC) for the specified instrument over the given timeframe. Identify Fair Value Gaps (FVGs), which occur when there is a gap between the high of one candle and the low of a subsequent candle (bullish FVG) or the low of one candle and the high of a subsequent candle (bearish FVG), typically spanning significant price movement with no overlap. Use the OHLC data to determine timestamps and price ranges. Provide the following:

1. Timestamp of the FVG formation.
2. Type (bullish or bearish).
3. Price range of the gap (e.g., high of first candle to low of third candle).
4. Confidence score (0-100%) based on gap size and market context (e.g., near key levels or after a Break of Structure).
5. Recommended action: Expect price to return to fill the FVG or use it as a target.

**Input Data**:
- Instrument: {symbol}
- Timeframe: {timeframe}
- OHLC Data: {ohlc_data}
- Recent Break of Structure: {bos_data}

**Output Format**:
- FVG: {"timestamp": "YYYY-MM-DDTHH:MM:SS", "type": "bullish/bearish", "price_range": {"high": float, "low": float}, "confidence": int, "action": "string"}
"""

# Initialize MT5
if not mt5.initialize(login=210388114, password="DruzaNastyW#1", server="Exness-MT5Trial9"):
    print("initialize() failed, error code =", mt5.last_error())
    quit()

# Verify connection
if not mt5.terminal_info():
    print("MT5 terminal not connected")
    quit()

# Initialize client
client = Client(
    api_host=GROK_API_URL,
    api_key=GROK_API_KEY
)

def fetch_ohlc_data(symbol, timeframe, limit=100):
    """Fetch OHLC data from MT5 with debug logging."""
    try:
        # Check if symbol is available
        if not mt5.symbol_select(symbol, True):
            print(f"Symbol {symbol} not found in Market Watch")
            return []
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, limit)
        print(f"Raw rates length for {symbol} ({'15M' if timeframe == mt5.TIMEFRAME_M15 else '4H'}): {len(rates)}")  # Debug
        if len(rates) == 0:
            print(f"Fetched bars for {symbol} ({'15M' if timeframe == mt5.TIMEFRAME_M15 else '4H'}): Empty DataFrame")
            return []
        ohlc_data = [
            {
                "timestamp": datetime.fromtimestamp(rate[0]).isoformat(),  # Use isoformat for consistent string
                "open": float(rate[1]),
                "high": float(rate[2]),
                "low": float(rate[3]),
                "close": float(rate[4]),
                "volume": int(rate[5])
            } for rate in rates
        ]
        print(f"Fetched bars for {symbol} ({'15M' if timeframe == mt5.TIMEFRAME_M15 else '4H'}): {len(ohlc_data)} candles")
        return ohlc_data
    except Exception as e:
        print(f"Error fetching OHLC data for {symbol} ({'15M' if timeframe == mt5.TIMEFRAME_M15 else '4H'}): {e}")
        return []

def fetch_support_resistance(symbol, timeframe):
    """Calculate support/resistance levels (placeholder)."""
    ohlc_data = fetch_ohlc_data(symbol, timeframe, limit=50)
    if not ohlc_data:
        return {"support": 0, "resistance": 0}
    highs = [candle["high"] for candle in ohlc_data]
    lows = [candle["low"] for candle in ohlc_data]
    return {"support": min(lows), "resistance": max(highs)}  # Simplified example

def fetch_break_of_structure(symbol, timeframe):
    """Detect Break of Structure on higher timeframe (placeholder)."""
    ohlc_data = fetch_ohlc_data(symbol, timeframe, limit=20)
    if not ohlc_data or len(ohlc_data) < 2:
        print(f"No sufficient data for Break of Structure on {symbol} ({'15M' if timeframe == mt5.TIMEFRAME_M15 else '4H'})")
        return {"type": "none", "level": 0, "timestamp": ""}
    latest_candle = ohlc_data[-1]
    previous_candle = ohlc_data[-2]
    if latest_candle["close"] > previous_candle["high"]:
        return {"type": "bullish", "level": previous_candle["high"], "timestamp": latest_candle["timestamp"]}
    elif latest_candle["close"] < previous_candle["low"]:
        return {"type": "bearish", "level": previous_candle["low"], "timestamp": latest_candle["timestamp"]}
    return {"type": "none", "level": 0, "timestamp": ""}

def call_grok_api(prompt, data):
    messages = [
        {"role": "system", "content": "You are an expert in Smart Money Concepts (SMC) trading."},
        {"role": "user", "content": prompt.format(**data)}
    ]
    response = client.chat.completions.create(
        model="grok-3",
        messages=messages,
        temperature=0
    )
    return response.choices[0].message.content
    
def get_directional_bias(symbol):
    """Determine market direction from 4H timeframe."""
    bos_data = fetch_break_of_structure(symbol, TIMEFRAME_4H)
    return bos_data.get("type", "none")

def analyze_smc(symbol, timeframe=TIMEFRAME_15M):
    """Analyze OBs and FVGs on 15-minute timeframe, filtered by 4H bias."""
    bias = get_directional_bias(symbol)
    ohlc_data = fetch_ohlc_data(symbol, timeframe)
    sr_levels = fetch_support_resistance(symbol, timeframe)
    bos_data = fetch_break_of_structure(symbol, timeframe)

    data = {
        "symbol": symbol,
        "timeframe": str(timeframe),
        "ohlc_data": json.dumps(ohlc_data),  # Should now serialize correctly
        "sr_levels": json.dumps(sr_levels),
        "bos_data": json.dumps(bos_data)
    }

    order_block_result = call_grok_api(ORDER_BLOCK_PROMPT, data) if ohlc_data else {}
    if order_block_result and (bias != "none" and order_block_result.get("type") != bias):
        order_block_result = {}

    fvg_result = call_grok_api(FVG_PROMPT, data) if ohlc_data else {}
    if fvg_result and (bias != "none" and fvg_result.get("type") != bias):
        fvg_result = {}

    return order_block_result, fvg_result

def save_signal(signal):
    """Save signal to a file for manual review."""
    with open(SIGNAL_FILE, "a") as f:
        f.write(f"{datetime.now()} - {signal}\n")

def trading_decision_engine(symbol, order_block, fvg):
    """Generate trading signals for manual review."""
    signals = []
    bias = get_directional_bias(symbol)
    if order_block and order_block.get("confidence", 0) > CONFIDENCE_THRESHOLD and (bias == "none" or order_block.get("type") == bias):
        signal = f"Symbol: {symbol}, Type: {order_block['type']}, Entry: {order_block['price_range']['low' if order_block['type'] == 'bullish' else 'high']:.5f}, Action: {order_block['action']}, Confidence: {order_block['confidence']}%, Timestamp: {order_block['timestamp']}, Signal: Order Block"
        signals.append(signal)
        save_signal(signal)
        print(f"Manual Signal: {signal}")
    if fvg and fvg.get("confidence", 0) > CONFIDENCE_THRESHOLD and (bias == "none" or fvg.get("type") == bias):
        signal = f"Symbol: {symbol}, Type: {fvg['type']}, Target: {fvg['price_range']['low' if fvg['type'] == 'bullish' else 'high']:.5f}, Action: {fvg['action']}, Confidence: {fvg['confidence']}%, Timestamp: {fvg['timestamp']}, Signal: FVG"
        signals.append(signal)
        save_signal(signal)
        print(f"Manual Signal: {signal}")
    return signals

def main():
    """Main loop to monitor and generate manual trading signals."""
    with open(SIGNAL_FILE, "w") as f:
        f.write("SMC Trading Signals - Started at " + datetime.now().strftime("%Y-%m-%d %H:%M:%S SAST") + "\n")
    while True:
        print(f"Analysis cycle at {datetime.now().strftime('%Y-%m-%d %H:%M:%S SAST')}")
        for symbol in SYMBOLS:
            print(f"Analyzing {symbol}")
            order_block, fvg = analyze_smc(symbol, TIMEFRAME_15M)
            trading_decision_engine(symbol, order_block, fvg)
        time.sleep(900)  # Sleep for 15 minutes

if __name__ == "__main__":
    main()