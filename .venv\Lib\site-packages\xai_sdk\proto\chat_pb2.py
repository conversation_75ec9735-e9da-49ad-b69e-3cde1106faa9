# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: chat.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from . import sampler_public_pb2 as sampler__public__pb2
from . import prod_search_pb2 as prod__search__pb2
from . import x_entities_pb2 as x__entities__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nchat.proto\x12\nprompt_ide\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x14sampler_public.proto\x1a\x11prod_search.proto\x1a\x10x_entities.proto\"I\n\x19RegenerateResponseRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\x13\n\x0bresponse_id\x18\x02 \x01(\t\"7\n\x19\x43reateConversationRequest\x12\x1a\n\x12system_prompt_name\x18\x01 \x01(\t\"1\n\x16GetConversationRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\"4\n\x19\x44\x65leteConversationRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\"r\n\x18ListConversationsRequest\x12\x16\n\tpage_size\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x1c\n\x0fnext_page_token\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x0c\n\n_page_sizeB\x12\n\x10_next_page_token\"L\n\x19ListConversationsResponse\x12/\n\rconversations\x18\x01 \x03(\x0b\x32\x18.prompt_ide.Conversation\"I\n\x14GenerateTitleRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\x18\n\x10leaf_response_id\x18\x02 \x01(\t\"*\n\x15GenerateTitleResponse\x12\x11\n\tnew_title\x18\x01 \x01(\t\"p\n\x17\x41\x64\x64ModelResponseRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07partial\x18\x03 \x01(\x08\x12\x1a\n\x12parent_response_id\x18\x04 \x01(\t\"T\n\x19UpdateConversationRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07starred\x18\x03 \x01(\x08\"\x89\x01\n\rSearchContext\x12\x18\n\x10web_search_query\x18\x01 \x01(\t\x12\x1c\n\x14web_formatted_chunks\x18\x02 \x01(\t\x12\x16\n\x0ex_search_query\x18\x03 \x01(\t\x12\x1a\n\x12x_formatted_chunks\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x05 \x01(\t\"\xf1\x01\n\x10ResponseDebugLog\x12\x1a\n\x12system_prompt_name\x18\x01 \x01(\t\x12\x16\n\x0e\x64isable_search\x18\x02 \x01(\x08\x12\x11\n\tweb_query\x18\x03 \x01(\t\x12\x0f\n\x07x_query\x18\x04 \x01(\t\x12\x13\n\x0bimage_query\x18\x05 \x01(\t\x12\x0e\n\x06prompt\x18\x07 \x01(\t\x12\'\n\x06inputs\x18\t \x03(\x0b\x32\x17.prompt_ide.PromptInput\x12\x31\n\x0esearch_context\x18\x08 \x01(\x0b\x32\x19.prompt_ide.SearchContextJ\x04\x08\x06\x10\x07\"\xd5\x01\n\x11\x41\x64\x64itionalOptions\x12:\n\x17injected_search_context\x18\x01 \x01(\x0b\x32\x19.prompt_ide.SearchContext\x12\x18\n\x0btemperature\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x11\n\x04seed\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x1c\n\x14ignore_time_location\x18\x04 \x01(\x08\x12\x14\n\x07max_len\x18\x05 \x01(\x05H\x02\x88\x01\x01\x42\x0e\n\x0c_temperatureB\x07\n\x05_seedB\n\n\x08_max_len\"\xed\x02\n\x12\x41\x64\x64ResponseRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nmodel_name\x18\x03 \x01(\t\x12\x1a\n\x12parent_response_id\x18\x05 \x01(\t\x12\x1a\n\x12system_prompt_name\x18\x07 \x01(\t\x12\x16\n\x0e\x64isable_search\x18\x08 \x01(\x08\x12\x1f\n\x17\x65nable_image_generation\x18\t \x01(\x08\x12\x12\n\nforce_rust\x18\n \x01(\x08\x12\x19\n\x11image_attachments\x18\x10 \x03(\t\x12\x1a\n\x12return_image_bytes\x18\x11 \x01(\x08\x12\x39\n\x12\x61\x64\x64itional_options\x18\x0f \x01(\x0b\x32\x1d.prompt_ide.AdditionalOptionsJ\x04\x08\x04\x10\x05J\x04\x08\x06\x10\x07J\x04\x08\x0b\x10\x0cJ\x04\x08\x0c\x10\rJ\x04\x08\r\x10\x0eJ\x04\x08\x0e\x10\x0f\"\xae\x04\n\x13\x41\x64\x64ResponseResponse\x12-\n\ruser_response\x18\x01 \x01(\x0b\x32\x14.prompt_ide.ResponseH\x00\x12\x31\n\x05token\x18\x02 \x01(\x0b\x32 .prompt_ide.SampleTokensResponseH\x00\x12.\n\x0emodel_response\x18\x03 \x01(\x0b\x32\x14.prompt_ide.ResponseH\x00\x12/\n\x0cquery_action\x18\x04 \x01(\x0b\x32\x17.prompt_ide.QueryActionH\x00\x12H\n\x19image_generation_response\x18\x05 \x01(\x0b\x32#.prompt_ide.ImageGenerationResponseH\x00\x12\x36\n\x10x_search_results\x18\x06 \x01(\x0b\x32\x1a.prompt_ide.XSearchResultsH\x00\x12U\n cached_image_generation_response\x18\x07 \x01(\x0b\x32).prompt_ide.CachedImageGenerationResponseH\x00\x12:\n\x12web_search_results\x18\x08 \x01(\x0b\x32\x1c.prompt_ide.WebSearchResultsH\x00\x12\x33\n\x0esearch_context\x18\t \x01(\x0b\x32\x19.prompt_ide.SearchContextH\x00\x42\n\n\x08response\"*\n\x0bQueryAction\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\"\x8a\x02\n\x0c\x43onversation\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07starred\x18\x06 \x01(\x08\x12/\n\x0b\x63reate_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bmodify_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\tresponses\x18\x05 \x03(\x0b\x32\x14.prompt_ide.Response\x12\x1f\n\x12system_prompt_name\x18\x07 \x01(\tH\x00\x88\x01\x01\x42\x15\n\x13_system_prompt_name\"\x81\x03\n\x08Response\x12\x13\n\x0bresponse_id\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0e\n\x06sender\x18\x03 \x01(\t\x12/\n\x0b\x63reate_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12parent_response_id\x18\x05 \x01(\t\x12\x0e\n\x06manual\x18\x06 \x01(\x08\x12\x0f\n\x07partial\x18\x07 \x01(\x08\x12\x0e\n\x06shared\x18\x08 \x01(\x08\x12\r\n\x05query\x18\t \x01(\t\x12\x12\n\nquery_type\x18\n \x01(\t\x12\x11\n\txpost_ids\x18\x0b \x03(\t\x12!\n\x06xposts\x18\x0c \x03(\x0b\x32\x11.prompt_ide.XPost\x12\x1c\n\x14generated_image_urls\x18\r \x03(\t\x12/\n\tdebug_log\x18\x0e \x01(\x0b\x32\x1c.prompt_ide.ResponseDebugLog\x12\x19\n\x11image_attachments\x18\x0f \x03(\t\"N\n\x17ImageGenerationResponse\x12\x1d\n\x15generated_image_bytes\x18\x01 \x01(\x0c\x12\x14\n\x0c\x63ontent_type\x18\x02 \x01(\t\"2\n\x1d\x43\x61\x63hedImageGenerationResponse\x12\x11\n\timage_url\x18\x01 \x01(\t\"H\n\x18ShareConversationRequest\x12\x17\n\x0f\x63onversation_id\x18\x01 \x01(\t\x12\x13\n\x0bresponse_id\x18\x02 \x01(\t\".\n\x19ShareConversationResponse\x12\x11\n\tshared_id\x18\x01 \x01(\t\"\x83\x01\n\x1aSearchConversationsRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x16\n\tpage_size\x18\x02 \x01(\x03H\x00\x88\x01\x01\x12\x1c\n\x0fnext_page_token\x18\x03 \x01(\tH\x01\x88\x01\x01\x42\x0c\n\n_page_sizeB\x12\n\x10_next_page_token\"N\n\x1bSearchConversationsResponse\x12/\n\rconversations\x18\x01 \x03(\x0b\x32\x18.prompt_ide.Conversation2\x91\x07\n\x04\x43hat\x12W\n\x12\x43reateConversation\x12%.prompt_ide.CreateConversationRequest\x1a\x18.prompt_ide.Conversation\"\x00\x12Q\n\x0fGetConversation\x12\".prompt_ide.GetConversationRequest\x1a\x18.prompt_ide.Conversation\"\x00\x12\x62\n\x11ListConversations\x12$.prompt_ide.ListConversationsRequest\x1a%.prompt_ide.ListConversationsResponse\"\x00\x12R\n\x0b\x41\x64\x64Response\x12\x1e.prompt_ide.AddResponseRequest\x1a\x1f.prompt_ide.AddResponseResponse\"\x00\x30\x01\x12U\n\x12\x44\x65leteConversation\x12%.prompt_ide.DeleteConversationRequest\x1a\x16.google.protobuf.Empty\"\x00\x12V\n\rGenerateTitle\x12 .prompt_ide.GenerateTitleRequest\x1a!.prompt_ide.GenerateTitleResponse\"\x00\x12W\n\x12UpdateConversation\x12%.prompt_ide.UpdateConversationRequest\x1a\x18.prompt_ide.Conversation\"\x00\x12O\n\x10\x41\x64\x64ModelResponse\x12#.prompt_ide.AddModelResponseRequest\x1a\x14.prompt_ide.Response\"\x00\x12\x62\n\x11ShareConversation\x12$.prompt_ide.ShareConversationRequest\x1a%.prompt_ide.ShareConversationResponse\"\x00\x12h\n\x13SearchConversations\x12&.prompt_ide.SearchConversationsRequest\x1a\'.prompt_ide.SearchConversationsResponse\"\x00\x42\x1cZ\x1ax.ai/prompt_ide;prompt_ideb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'chat_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\032x.ai/prompt_ide;prompt_ide'
  _globals['_REGENERATERESPONSEREQUEST']._serialized_start=147
  _globals['_REGENERATERESPONSEREQUEST']._serialized_end=220
  _globals['_CREATECONVERSATIONREQUEST']._serialized_start=222
  _globals['_CREATECONVERSATIONREQUEST']._serialized_end=277
  _globals['_GETCONVERSATIONREQUEST']._serialized_start=279
  _globals['_GETCONVERSATIONREQUEST']._serialized_end=328
  _globals['_DELETECONVERSATIONREQUEST']._serialized_start=330
  _globals['_DELETECONVERSATIONREQUEST']._serialized_end=382
  _globals['_LISTCONVERSATIONSREQUEST']._serialized_start=384
  _globals['_LISTCONVERSATIONSREQUEST']._serialized_end=498
  _globals['_LISTCONVERSATIONSRESPONSE']._serialized_start=500
  _globals['_LISTCONVERSATIONSRESPONSE']._serialized_end=576
  _globals['_GENERATETITLEREQUEST']._serialized_start=578
  _globals['_GENERATETITLEREQUEST']._serialized_end=651
  _globals['_GENERATETITLERESPONSE']._serialized_start=653
  _globals['_GENERATETITLERESPONSE']._serialized_end=695
  _globals['_ADDMODELRESPONSEREQUEST']._serialized_start=697
  _globals['_ADDMODELRESPONSEREQUEST']._serialized_end=809
  _globals['_UPDATECONVERSATIONREQUEST']._serialized_start=811
  _globals['_UPDATECONVERSATIONREQUEST']._serialized_end=895
  _globals['_SEARCHCONTEXT']._serialized_start=898
  _globals['_SEARCHCONTEXT']._serialized_end=1035
  _globals['_RESPONSEDEBUGLOG']._serialized_start=1038
  _globals['_RESPONSEDEBUGLOG']._serialized_end=1279
  _globals['_ADDITIONALOPTIONS']._serialized_start=1282
  _globals['_ADDITIONALOPTIONS']._serialized_end=1495
  _globals['_ADDRESPONSEREQUEST']._serialized_start=1498
  _globals['_ADDRESPONSEREQUEST']._serialized_end=1863
  _globals['_ADDRESPONSERESPONSE']._serialized_start=1866
  _globals['_ADDRESPONSERESPONSE']._serialized_end=2424
  _globals['_QUERYACTION']._serialized_start=2426
  _globals['_QUERYACTION']._serialized_end=2468
  _globals['_CONVERSATION']._serialized_start=2471
  _globals['_CONVERSATION']._serialized_end=2737
  _globals['_RESPONSE']._serialized_start=2740
  _globals['_RESPONSE']._serialized_end=3125
  _globals['_IMAGEGENERATIONRESPONSE']._serialized_start=3127
  _globals['_IMAGEGENERATIONRESPONSE']._serialized_end=3205
  _globals['_CACHEDIMAGEGENERATIONRESPONSE']._serialized_start=3207
  _globals['_CACHEDIMAGEGENERATIONRESPONSE']._serialized_end=3257
  _globals['_SHARECONVERSATIONREQUEST']._serialized_start=3259
  _globals['_SHARECONVERSATIONREQUEST']._serialized_end=3331
  _globals['_SHARECONVERSATIONRESPONSE']._serialized_start=3333
  _globals['_SHARECONVERSATIONRESPONSE']._serialized_end=3379
  _globals['_SEARCHCONVERSATIONSREQUEST']._serialized_start=3382
  _globals['_SEARCHCONVERSATIONSREQUEST']._serialized_end=3513
  _globals['_SEARCHCONVERSATIONSRESPONSE']._serialized_start=3515
  _globals['_SEARCHCONVERSATIONSRESPONSE']._serialized_end=3593
  _globals['_CHAT']._serialized_start=3596
  _globals['_CHAT']._serialized_end=4509
# @@protoc_insertion_point(module_scope)
