import requests
import json
import time
from datetime import datetime
from flask import Flask, request, jsonify
import pandas as pd
import numpy as np
from typing import Dict, List, Optional

class GroqAIAnalyzer:
    """AI analyzer using Groq API for SMC strategy analysis"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.groq.com/openai/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def analyze_market_structure(self, market_data: Dict) -> Dict:
        """Analyze market using SMC principles"""
        
        prompt = f"""
        As an expert SMC (Smart Money Concepts) trader, analyze this market data:
        
        Current Price: {market_data.get('price', 'N/A')}
        Symbol: {market_data.get('symbol', 'N/A')}
        Timeframe: {market_data.get('timeframe', 'N/A')}
        Recent Highs: {market_data.get('recent_highs', [])}
        Recent Lows: {market_data.get('recent_lows', [])}
        Volume: {market_data.get('volume', 'N/A')}
        
        Provide SMC analysis in this exact JSON format:
        {{
            "market_structure": "bullish/bearish/ranging",
            "bos_detected": true/false,
            "order_blocks": [
                {{
                    "type": "bullish/bearish",
                    "price_level": 0.0,
                    "strength": "high/medium/low"
                }}
            ],
            "fair_value_gaps": [
                {{
                    "type": "bullish/bearish",
                    "upper_level": 0.0,
                    "lower_level": 0.0
                }}
            ],
            "liquidity_zones": [
                {{
                    "type": "buy/sell",
                    "price_level": 0.0
                }}
            ],
            "entry_signal": {{
                "action": "buy/sell/wait",
                "entry_price": 0.0,
                "stop_loss": 0.0,
                "take_profit": 0.0,
                "confidence": 0.85
            }},
            "analysis_summary": "Brief explanation of the analysis"
        }}
        
        Only return the JSON, no additional text.
        """
        
        try:
            payload = {
                "model": "llama-3.1-70b-versatile",  # Fast Groq model
                "messages": [
                    {"role": "system", "content": "You are an expert SMC trader. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            analysis_text = result['choices'][0]['message']['content']
            
            # Parse JSON response
            analysis = json.loads(analysis_text.strip())
            return analysis
            
        except Exception as e:
            print(f"AI Analysis Error: {e}")
            return self._get_default_analysis()
    
    def _get_default_analysis(self) -> Dict:
        """Return default analysis structure when AI fails"""
        return {
            "market_structure": "ranging",
            "bos_detected": False,
            "order_blocks": [],
            "fair_value_gaps": [],
            "liquidity_zones": [],
            "entry_signal": {
                "action": "wait",
                "entry_price": 0.0,
                "stop_loss": 0.0,
                "take_profit": 0.0,
                "confidence": 0.0
            },
            "analysis_summary": "Analysis failed, waiting for better setup"
        }

class SMCTradingBot:
    """Main trading bot class"""
    
    def __init__(self, groq_api_key: str):
        self.ai_analyzer = GroqAIAnalyzer(groq_api_key)
        self.active_trades = []
        self.market_data_history = []
        self.signals_log = []
        
    def process_tradingview_webhook(self, webhook_data: Dict) -> Dict:
        """Process incoming TradingView webhook data"""
        
        try:
            # Extract market data from TradingView webhook
            market_data = {
                'symbol': webhook_data.get('symbol', ''),
                'price': float(webhook_data.get('close', 0)),
                'timeframe': webhook_data.get('timeframe', '1h'),
                'volume': webhook_data.get('volume', 0),
                'high': float(webhook_data.get('high', 0)),
                'low': float(webhook_data.get('low', 0)),
                'open': float(webhook_data.get('open', 0)),
                'timestamp': datetime.now().isoformat()
            }
            
            # Add recent price history for analysis
            self.market_data_history.append(market_data)
            if len(self.market_data_history) > 100:  # Keep last 100 candles
                self.market_data_history.pop(0)
            
            # Calculate recent highs and lows for SMC analysis
            recent_data = self.market_data_history[-20:]  # Last 20 candles
            market_data['recent_highs'] = [d['high'] for d in recent_data]
            market_data['recent_lows'] = [d['low'] for d in recent_data]
            
            # Get AI analysis
            analysis = self.ai_analyzer.analyze_market_structure(market_data)
            
            # Process the analysis and generate signals
            signal = self._process_analysis(analysis, market_data)
            
            # Log the signal
            self.signals_log.append({
                'timestamp': datetime.now().isoformat(),
                'symbol': market_data['symbol'],
                'signal': signal,
                'analysis': analysis
            })
            
            return signal
            
        except Exception as e:
            print(f"Webhook processing error: {e}")
            return {'error': str(e)}
    
    def _process_analysis(self, analysis: Dict, market_data: Dict) -> Dict:
        """Process AI analysis and create trading signal"""
        
        entry_signal = analysis.get('entry_signal', {})
        action = entry_signal.get('action', 'wait')
        confidence = entry_signal.get('confidence', 0)
        
        signal = {
            'symbol': market_data['symbol'],
            'action': action,
            'current_price': market_data['price'],
            'timestamp': datetime.now().isoformat(),
            'confidence': confidence,
            'market_structure': analysis.get('market_structure', 'unknown'),
            'bos_detected': analysis.get('bos_detected', False),
            'analysis_summary': analysis.get('analysis_summary', ''),
        }
        
        # Add entry details if signal is buy/sell
        if action in ['buy', 'sell'] and confidence > 0.7:
            signal.update({
                'entry_price': entry_signal.get('entry_price', market_data['price']),
                'stop_loss': entry_signal.get('stop_loss', 0),
                'take_profit': entry_signal.get('take_profit', 0),
                'risk_reward_ratio': self._calculate_rr_ratio(
                    entry_signal.get('entry_price', 0),
                    entry_signal.get('stop_loss', 0),
                    entry_signal.get('take_profit', 0)
                )
            })
            
            # Add to active trades
            self.active_trades.append(signal)
        
        return signal
    
    def _calculate_rr_ratio(self, entry: float, stop_loss: float, take_profit: float) -> float:
        """Calculate risk-reward ratio"""
        if entry == 0 or stop_loss == 0 or take_profit == 0:
            return 0
        
        risk = abs(entry - stop_loss)
        reward = abs(take_profit - entry)
        
        return reward / risk if risk > 0 else 0
    
    def get_signals_summary(self) -> Dict:
        """Get summary of recent signals"""
        recent_signals = self.signals_log[-10:]  # Last 10 signals
        
        return {
            'total_signals': len(self.signals_log),
            'active_trades': len(self.active_trades),
            'recent_signals': recent_signals,
            'last_updated': datetime.now().isoformat()
        }

# Flask web server to receive TradingView webhooks
app = Flask(__name__)

# Initialize bot (you'll need to add your Groq API key)
GROQ_API_KEY = "********************************************************"  # Get from https://console.groq.com
trading_bot = SMCTradingBot(GROQ_API_KEY)

@app.route('/webhook', methods=['POST'])
def tradingview_webhook():
    """Endpoint to receive TradingView webhooks"""
    try:
        data = request.get_json()
        signal = trading_bot.process_tradingview_webhook(data)
        
        print(f"New Signal: {signal}")
        
        return jsonify({
            'status': 'success',
            'signal': signal
        })
        
    except Exception as e:
        print(f"Webhook error: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 400

@app.route('/signals', methods=['GET'])
def get_signals():
    """Get trading signals summary"""
    summary = trading_bot.get_signals_summary()
    return jsonify(summary)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("Starting AI SMC Trading Bot...")
    print("Webhook URL: http://localhost:5000/webhook")
    print("Signals URL: http://localhost:5000/signals")
    
    # Run Flask server
    app.run(host='0.0.0.0', port=5000, debug=True)