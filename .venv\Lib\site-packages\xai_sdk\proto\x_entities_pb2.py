# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: x_entities.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10x_entities.proto\x12\nprompt_ide\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd0\x02\n\x05XPost\x12\x10\n\x08username\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04text\x18\x03 \x01(\t\x12/\n\x0b\x63reate_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11profile_image_url\x18\x05 \x01(\t\x12\x0f\n\x07post_id\x18\x06 \x01(\t\x12\x13\n\x0b\x63itation_id\x18\x08 \x01(\t\x12!\n\x06parent\x18\t \x01(\x0b\x32\x11.prompt_ide.XPost\x12\x1b\n\x0eparent_post_id\x18\n \x01(\tH\x00\x88\x01\x01\x12\x1a\n\rquote_post_id\x18\x0b \x01(\tH\x01\x88\x01\x01\x12 \n\x05quote\x18\x0c \x01(\x0b\x32\x11.prompt_ide.XPostB\x11\n\x0f_parent_post_idB\x10\n\x0e_quote_post_idJ\x04\x08\x07\x10\x08\"\x87\x02\n\nXTrendPost\x12\x15\n\rauthor_handle\x18\x01 \x01(\t\x12\x13\n\x0b\x61uthor_name\x18\x02 \x01(\t\x12\x15\n\rauthor_avatar\x18\x03 \x01(\t\x12\x19\n\x11\x61uthor_nfollowers\x18\x04 \x01(\x05\x12\x0f\n\x07rest_id\x18\x05 \x01(\t\x12\x10\n\x08nreplies\x18\x06 \x01(\x05\x12\x0c\n\x04text\x18\x07 \x01(\t\x12\x12\n\ncreated_ts\x18\x08 \x01(\x03\x12\x10\n\x08nreposts\x18\t \x01(\x05\x12\x0c\n\x04imgs\x18\n \x03(\t\x12\x0c\n\x04vids\x18\x0b \x03(\t\x12(\n\x04\x63\x61rd\x18\x0c \x01(\x0b\x32\x1a.prompt_ide.XTrendPostCard\"\x96\x01\n\x0cXTrendResult\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04hook\x18\x02 \x01(\t\x12\x0f\n\x07summary\x18\x03 \x01(\t\x12\x0f\n\x07rest_id\x18\x04 \x01(\t\x12%\n\x05posts\x18\x05 \x03(\x0b\x32\x16.prompt_ide.XTrendPost\x12\x11\n\tthumbnail\x18\x06 \x01(\t\x12\x0e\n\x06header\x18\x07 \x01(\t\"*\n\x0eXTrendPostCard\x12\x0b\n\x03img\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\tB\x1cZ\x1ax.ai/prompt_ide;prompt_ideb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'x_entities_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\032x.ai/prompt_ide;prompt_ide'
  _globals['_XPOST']._serialized_start=66
  _globals['_XPOST']._serialized_end=402
  _globals['_XTRENDPOST']._serialized_start=405
  _globals['_XTRENDPOST']._serialized_end=668
  _globals['_XTRENDRESULT']._serialized_start=671
  _globals['_XTRENDRESULT']._serialized_end=821
  _globals['_XTRENDPOSTCARD']._serialized_start=823
  _globals['_XTRENDPOSTCARD']._serialized_end=865
# @@protoc_insertion_point(module_scope)
