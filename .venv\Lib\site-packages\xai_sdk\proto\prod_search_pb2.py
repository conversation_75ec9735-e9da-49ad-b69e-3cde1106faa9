# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: prod_search.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from . import x_entities_pb2 as x__entities__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11prod_search.proto\x12\nprompt_ide\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x10x_entities.proto\";\n\x18GetXCuratedTrendsRequest\x12\r\n\x05limit\x18\x01 \x01(\x05\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\t\"E\n\x19GetXCuratedTrendsResponse\x12(\n\x06trends\x18\x01 \x03(\x0b\x32\x18.prompt_ide.XTrendResult\"\"\n\x14GetXTrendByIdRequest\x12\n\n\x02id\x18\x01 \x01(\t\"@\n\x15GetXTrendByIdResponse\x12\'\n\x05trend\x18\x01 \x01(\x0b\x32\x18.prompt_ide.XTrendResult\"\xb2\x01\n\rSearchRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x15\n\rforce_refresh\x18\x02 \x01(\x08\x12\x12\n\nmodel_name\x18\x03 \x01(\t\x12\x0e\n\x06prompt\x18\x04 \x01(\t\x12\x11\n\tsearch_id\x18\x05 \x01(\t\x12\x17\n\x0f\x62\x61\x63kend_address\x18\x06 \x01(\t\x12\x12\n\nforce_rust\x18\x07 \x01(\x08\x12\x17\n\x0fimage_generator\x18\x08 \x01(\t\"\xe1\x02\n\x0eSearchResponse\x12\x36\n\x10x_search_results\x18\x01 \x01(\x0b\x32\x1a.prompt_ide.XSearchResultsH\x00\x12:\n\x12web_search_results\x18\x02 \x01(\x0b\x32\x1c.prompt_ide.WebSearchResultsH\x00\x12\x0e\n\x04text\x18\x03 \x01(\tH\x00\x12\x46\n\x18image_generation_results\x18\x04 \x01(\x0b\x32\".prompt_ide.ImageGenerationResultsH\x00\x12\x34\n\x0fx_trend_results\x18\x05 \x01(\x0b\x32\x19.prompt_ide.XTrendResultsH\x00\x12\x13\n\tsearch_id\x18\x06 \x01(\tH\x00\x12-\n\x05\x64\x65\x62ug\x18\x07 \x01(\x0b\x32\x1c.prompt_ide.DebugInformationH\x00\x42\t\n\x07payload\"4\n\x0eXSearchResults\x12\"\n\x07results\x18\x01 \x03(\x0b\x32\x11.prompt_ide.XPost\":\n\rXTrendResults\x12)\n\x07results\x18\x01 \x03(\x0b\x32\x18.prompt_ide.XTrendResult\"\xe6\x01\n\x0fWebSearchResult\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07preview\x18\x04 \x01(\t\x12\x1a\n\x12search_engine_text\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x11\n\tsite_name\x18\x07 \x01(\t\x12\x16\n\x0emetadata_title\x18\x08 \x01(\t\x12\x0f\n\x07\x63reator\x18\t \x01(\t\x12\r\n\x05image\x18\n \x01(\t\x12\x0f\n\x07\x66\x61vicon\x18\x0b \x01(\t\x12\x13\n\x0b\x63itation_id\x18\x0c \x01(\tJ\x04\x08\x03\x10\x04\"@\n\x10WebSearchResults\x12,\n\x07results\x18\x01 \x03(\x0b\x32\x1b.prompt_ide.WebSearchResult\":\n\x15ImageGenerationResult\x12\x11\n\timage_url\x18\x01 \x01(\t\x12\x0e\n\x06prompt\x18\x02 \x01(\t\"e\n\x16ImageGenerationResults\x12\x32\n\x07results\x18\x01 \x03(\x0b\x32!.prompt_ide.ImageGenerationResult\x12\x17\n\x0fimage_generator\x18\x02 \x01(\t\"F\n\x1fGetMostRecentSearchQueryRequest\x12\r\n\x05limit\x18\x01 \x01(\x05\x12\x14\n\x0cquery_prefix\x18\x02 \x01(\t\"/\n\x0bSearchQuery\x12\x11\n\tsearch_id\x18\x01 \x01(\t\x12\r\n\x05query\x18\x02 \x01(\t\"?\n\x13SearchQueryResponse\x12(\n\x07queries\x18\x01 \x03(\x0b\x32\x17.prompt_ide.SearchQuery\"-\n\x18\x44\x65leteSearchQueryRequest\x12\x11\n\tsearch_id\x18\x01 \x01(\t\"%\n\x14\x43ompleteQueryRequest\x12\r\n\x05query\x18\x01 \x01(\t\"&\n\x15\x43ompleteQueryResponse\x12\r\n\x05query\x18\x01 \x01(\t\"P\n\x19SummarizeWebResultRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x17\n\x0f\x62\x61\x63kend_address\x18\x03 \x01(\t\",\n\x1aSummarizeWebResultResponse\x12\x0e\n\x06\x61nswer\x18\x01 \x01(\t\"h\n\x17WebsiteDebugInformation\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x10\n\x08raw_html\x18\x02 \x01(\t\x12\x13\n\x0bparsed_text\x18\x03 \x01(\t\x12\x19\n\x11summarized_chunks\x18\x04 \x03(\t\"i\n\x10\x44\x65\x62ugInformation\x12\x1e\n\x16\x66ormatted_model_prompt\x18\x01 \x01(\t\x12\x35\n\x08websites\x18\x02 \x03(\x0b\x32#.prompt_ide.WebsiteDebugInformation2\x8b\x05\n\x06Search\x12\x43\n\x06Search\x12\x19.prompt_ide.SearchRequest\x1a\x1a.prompt_ide.SearchResponse\"\x00\x30\x01\x12j\n\x18GetMostRecentSearchQuery\x12+.prompt_ide.GetMostRecentSearchQueryRequest\x1a\x1f.prompt_ide.SearchQueryResponse\"\x00\x12S\n\x11\x44\x65leteSearchQuery\x12$.prompt_ide.DeleteSearchQueryRequest\x1a\x16.google.protobuf.Empty\"\x00\x12V\n\rCompleteQuery\x12 .prompt_ide.CompleteQueryRequest\x1a!.prompt_ide.CompleteQueryResponse\"\x00\x12g\n\x12SummarizeWebResult\x12%.prompt_ide.SummarizeWebResultRequest\x1a&.prompt_ide.SummarizeWebResultResponse\"\x00\x30\x01\x12\x62\n\x11GetXCuratedTrends\x12$.prompt_ide.GetXCuratedTrendsRequest\x1a%.prompt_ide.GetXCuratedTrendsResponse\"\x00\x12V\n\rGetXTrendById\x12 .prompt_ide.GetXTrendByIdRequest\x1a!.prompt_ide.GetXTrendByIdResponse\"\x00\x42\x1cZ\x1ax.ai/prompt_ide;prompt_ideb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'prod_search_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\032x.ai/prompt_ide;prompt_ide'
  _globals['_GETXCURATEDTRENDSREQUEST']._serialized_start=113
  _globals['_GETXCURATEDTRENDSREQUEST']._serialized_end=172
  _globals['_GETXCURATEDTRENDSRESPONSE']._serialized_start=174
  _globals['_GETXCURATEDTRENDSRESPONSE']._serialized_end=243
  _globals['_GETXTRENDBYIDREQUEST']._serialized_start=245
  _globals['_GETXTRENDBYIDREQUEST']._serialized_end=279
  _globals['_GETXTRENDBYIDRESPONSE']._serialized_start=281
  _globals['_GETXTRENDBYIDRESPONSE']._serialized_end=345
  _globals['_SEARCHREQUEST']._serialized_start=348
  _globals['_SEARCHREQUEST']._serialized_end=526
  _globals['_SEARCHRESPONSE']._serialized_start=529
  _globals['_SEARCHRESPONSE']._serialized_end=882
  _globals['_XSEARCHRESULTS']._serialized_start=884
  _globals['_XSEARCHRESULTS']._serialized_end=936
  _globals['_XTRENDRESULTS']._serialized_start=938
  _globals['_XTRENDRESULTS']._serialized_end=996
  _globals['_WEBSEARCHRESULT']._serialized_start=999
  _globals['_WEBSEARCHRESULT']._serialized_end=1229
  _globals['_WEBSEARCHRESULTS']._serialized_start=1231
  _globals['_WEBSEARCHRESULTS']._serialized_end=1295
  _globals['_IMAGEGENERATIONRESULT']._serialized_start=1297
  _globals['_IMAGEGENERATIONRESULT']._serialized_end=1355
  _globals['_IMAGEGENERATIONRESULTS']._serialized_start=1357
  _globals['_IMAGEGENERATIONRESULTS']._serialized_end=1458
  _globals['_GETMOSTRECENTSEARCHQUERYREQUEST']._serialized_start=1460
  _globals['_GETMOSTRECENTSEARCHQUERYREQUEST']._serialized_end=1530
  _globals['_SEARCHQUERY']._serialized_start=1532
  _globals['_SEARCHQUERY']._serialized_end=1579
  _globals['_SEARCHQUERYRESPONSE']._serialized_start=1581
  _globals['_SEARCHQUERYRESPONSE']._serialized_end=1644
  _globals['_DELETESEARCHQUERYREQUEST']._serialized_start=1646
  _globals['_DELETESEARCHQUERYREQUEST']._serialized_end=1691
  _globals['_COMPLETEQUERYREQUEST']._serialized_start=1693
  _globals['_COMPLETEQUERYREQUEST']._serialized_end=1730
  _globals['_COMPLETEQUERYRESPONSE']._serialized_start=1732
  _globals['_COMPLETEQUERYRESPONSE']._serialized_end=1770
  _globals['_SUMMARIZEWEBRESULTREQUEST']._serialized_start=1772
  _globals['_SUMMARIZEWEBRESULTREQUEST']._serialized_end=1852
  _globals['_SUMMARIZEWEBRESULTRESPONSE']._serialized_start=1854
  _globals['_SUMMARIZEWEBRESULTRESPONSE']._serialized_end=1898
  _globals['_WEBSITEDEBUGINFORMATION']._serialized_start=1900
  _globals['_WEBSITEDEBUGINFORMATION']._serialized_end=2004
  _globals['_DEBUGINFORMATION']._serialized_start=2006
  _globals['_DEBUGINFORMATION']._serialized_end=2111
  _globals['_SEARCH']._serialized_start=2114
  _globals['_SEARCH']._serialized_end=2765
# @@protoc_insertion_point(module_scope)
