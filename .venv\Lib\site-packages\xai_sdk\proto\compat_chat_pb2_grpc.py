# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import compat_chat_pb2 as compat__chat__pb2


class ChatStub(object):
    """A chat API that is compatible with the one provided by OpenAI.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCompletion = channel.unary_unary(
                '/compat.chat.Chat/GetCompletion',
                request_serializer=compat__chat__pb2.GetCompletionsRequest.SerializeToString,
                response_deserializer=compat__chat__pb2.GetChatCompletionResponse.FromString,
                )
        self.GetCompletionChunk = channel.unary_stream(
                '/compat.chat.Chat/GetCompletionChunk',
                request_serializer=compat__chat__pb2.GetCompletionsRequest.SerializeToString,
                response_deserializer=compat__chat__pb2.GetChatCompletionChunk.FromString,
                )


class ChatServicer(object):
    """A chat API that is compatible with the one provided by OpenAI.
    """

    def GetCompletion(self, request, context):
        """Non-streaming version of the RPC.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCompletionChunk(self, request, context):
        """Streaming version of the RPC.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ChatServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCompletion': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCompletion,
                    request_deserializer=compat__chat__pb2.GetCompletionsRequest.FromString,
                    response_serializer=compat__chat__pb2.GetChatCompletionResponse.SerializeToString,
            ),
            'GetCompletionChunk': grpc.unary_stream_rpc_method_handler(
                    servicer.GetCompletionChunk,
                    request_deserializer=compat__chat__pb2.GetCompletionsRequest.FromString,
                    response_serializer=compat__chat__pb2.GetChatCompletionChunk.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'compat.chat.Chat', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Chat(object):
    """A chat API that is compatible with the one provided by OpenAI.
    """

    @staticmethod
    def GetCompletion(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/compat.chat.Chat/GetCompletion',
            compat__chat__pb2.GetCompletionsRequest.SerializeToString,
            compat__chat__pb2.GetChatCompletionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCompletionChunk(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/compat.chat.Chat/GetCompletionChunk',
            compat__chat__pb2.GetCompletionsRequest.SerializeToString,
            compat__chat__pb2.GetChatCompletionChunk.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
