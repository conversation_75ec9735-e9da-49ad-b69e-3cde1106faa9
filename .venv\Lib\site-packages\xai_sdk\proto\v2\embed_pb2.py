# fmt: off
# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: embed.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import image_pb2 as image__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x65mbed.proto\x12\x07xai_api\x1a\x0bimage.proto\"\x86\x01\n\x0c\x45mbedRequest\x12\"\n\x05input\x18\x01 \x03(\x0b\x32\x13.xai_api.EmbedInput\x12\r\n\x05model\x18\x02 \x01(\t\x12\x35\n\x0f\x65ncoding_format\x18\x03 \x01(\x0e\x32\x1c.xai_api.EmbedEncodingFormat\x12\x0c\n\x04user\x18\x04 \x01(\t\"V\n\nEmbedInput\x12\x10\n\x06string\x18\x01 \x01(\tH\x00\x12-\n\timage_url\x18\x02 \x01(\x0b\x32\x18.xai_api.ImageUrlContentH\x00\x42\x07\n\x05input\"g\n\rEmbedResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12&\n\nembeddings\x18\x02 \x03(\x0b\x32\x12.xai_api.Embedding\x12\"\n\x05usage\x18\x03 \x01(\x0b\x32\x13.xai_api.EmbedUsage\"F\n\tEmbedding\x12\r\n\x05index\x18\x01 \x01(\x05\x12*\n\nembeddings\x18\x02 \x03(\x0b\x32\x16.xai_api.FeatureVector\">\n\rFeatureVector\x12\x17\n\x0b\x66loat_array\x18\x01 \x03(\x02\x42\x02\x10\x01\x12\x14\n\x0c\x62\x61se64_array\x18\x02 \x01(\t\"G\n\nEmbedUsage\x12\x1b\n\x13num_text_embeddings\x18\x01 \x01(\x05\x12\x1c\n\x14num_image_embeddings\x18\x02 \x01(\x05*N\n\x13\x45mbedEncodingFormat\x12\x12\n\x0e\x46ORMAT_INVALID\x10\x00\x12\x10\n\x0c\x46ORMAT_FLOAT\x10\x01\x12\x11\n\rFORMAT_BASE64\x10\x02\x32\x44\n\x08\x45mbedder\x12\x38\n\x05\x45mbed\x12\x15.xai_api.EmbedRequest\x1a\x16.xai_api.EmbedResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embed_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _FEATUREVECTOR.fields_by_name['float_array']._options = None
  _FEATUREVECTOR.fields_by_name['float_array']._serialized_options = b'\020\001'
  _globals['_EMBEDENCODINGFORMAT']._serialized_start=576
  _globals['_EMBEDENCODINGFORMAT']._serialized_end=654
  _globals['_EMBEDREQUEST']._serialized_start=38
  _globals['_EMBEDREQUEST']._serialized_end=172
  _globals['_EMBEDINPUT']._serialized_start=174
  _globals['_EMBEDINPUT']._serialized_end=260
  _globals['_EMBEDRESPONSE']._serialized_start=262
  _globals['_EMBEDRESPONSE']._serialized_end=365
  _globals['_EMBEDDING']._serialized_start=367
  _globals['_EMBEDDING']._serialized_end=437
  _globals['_FEATUREVECTOR']._serialized_start=439
  _globals['_FEATUREVECTOR']._serialized_end=501
  _globals['_EMBEDUSAGE']._serialized_start=503
  _globals['_EMBEDUSAGE']._serialized_end=574
  _globals['_EMBEDDER']._serialized_start=656
  _globals['_EMBEDDER']._serialized_end=724
# @@protoc_insertion_point(module_scope)
