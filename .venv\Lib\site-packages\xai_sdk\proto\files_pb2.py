# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: files.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x66iles.proto\x12\nprompt_ide\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"]\n\x11UploadFileRequest\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\x0c\x12\x11\n\tmime_type\x18\x03 \x01(\t\x12\x11\n\toverwrite\x18\x04 \x01(\x08\"y\n\x0c\x46ileMetadata\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x12\n\nsize_bytes\x18\x02 \x01(\x05\x12\x11\n\tmime_type\x18\x03 \x01(\t\x12/\n\x0b\x63reate_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"=\n\x11RenameFileRequest\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x15\n\rnew_file_name\x18\x02 \x01(\t\"(\n\x13\x44ownloadFileRequest\x12\x11\n\tfile_name\x18\x01 \x01(\t\"I\n\nFileObject\x12*\n\x08metadata\x18\x01 \x01(\x0b\x32\x18.prompt_ide.FileMetadata\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\x0c\"s\n\x11ListFilesResponse\x12\x14\n\x0cstorage_used\x18\x01 \x01(\x05\x12\x1f\n\x17storage_available_total\x18\x02 \x01(\x05\x12\'\n\x05\x66iles\x18\x03 \x03(\x0b\x32\x18.prompt_ide.FileMetadata\"&\n\x11\x44\x65leteFileRequest\x12\x11\n\tfile_name\x18\x01 \x01(\t2\xf0\x02\n\x04\x46ile\x12G\n\nUploadFile\x12\x1d.prompt_ide.UploadFileRequest\x1a\x18.prompt_ide.FileMetadata\"\x00\x12I\n\x0c\x44ownloadFile\x12\x1f.prompt_ide.DownloadFileRequest\x1a\x16.prompt_ide.FileObject\"\x00\x12\x44\n\tListFiles\x12\x16.google.protobuf.Empty\x1a\x1d.prompt_ide.ListFilesResponse\"\x00\x12G\n\nRenameFile\x12\x1d.prompt_ide.RenameFileRequest\x1a\x18.prompt_ide.FileMetadata\"\x00\x12\x45\n\nDeleteFile\x12\x1d.prompt_ide.DeleteFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'files_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_UPLOADFILEREQUEST']._serialized_start=89
  _globals['_UPLOADFILEREQUEST']._serialized_end=182
  _globals['_FILEMETADATA']._serialized_start=184
  _globals['_FILEMETADATA']._serialized_end=305
  _globals['_RENAMEFILEREQUEST']._serialized_start=307
  _globals['_RENAMEFILEREQUEST']._serialized_end=368
  _globals['_DOWNLOADFILEREQUEST']._serialized_start=370
  _globals['_DOWNLOADFILEREQUEST']._serialized_end=410
  _globals['_FILEOBJECT']._serialized_start=412
  _globals['_FILEOBJECT']._serialized_end=485
  _globals['_LISTFILESRESPONSE']._serialized_start=487
  _globals['_LISTFILESRESPONSE']._serialized_end=602
  _globals['_DELETEFILEREQUEST']._serialized_start=604
  _globals['_DELETEFILEREQUEST']._serialized_end=642
  _globals['_FILE']._serialized_start=645
  _globals['_FILE']._serialized_end=1013
# @@protoc_insertion_point(module_scope)
