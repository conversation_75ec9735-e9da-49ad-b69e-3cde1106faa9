# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from . import prod_search_pb2 as prod__search__pb2


class SearchStub(object):
    """Exposes a simple search API.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Search = channel.unary_stream(
                '/prompt_ide.Search/Search',
                request_serializer=prod__search__pb2.SearchRequest.SerializeToString,
                response_deserializer=prod__search__pb2.SearchResponse.FromString,
                )
        self.GetMostRecentSearchQuery = channel.unary_unary(
                '/prompt_ide.Search/GetMostRecentSearchQuery',
                request_serializer=prod__search__pb2.GetMostRecentSearchQueryRequest.SerializeToString,
                response_deserializer=prod__search__pb2.SearchQueryResponse.FromString,
                )
        self.DeleteSearchQuery = channel.unary_unary(
                '/prompt_ide.Search/DeleteSearchQuery',
                request_serializer=prod__search__pb2.DeleteSearchQueryRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.CompleteQuery = channel.unary_unary(
                '/prompt_ide.Search/CompleteQuery',
                request_serializer=prod__search__pb2.CompleteQueryRequest.SerializeToString,
                response_deserializer=prod__search__pb2.CompleteQueryResponse.FromString,
                )
        self.SummarizeWebResult = channel.unary_stream(
                '/prompt_ide.Search/SummarizeWebResult',
                request_serializer=prod__search__pb2.SummarizeWebResultRequest.SerializeToString,
                response_deserializer=prod__search__pb2.SummarizeWebResultResponse.FromString,
                )
        self.GetXCuratedTrends = channel.unary_unary(
                '/prompt_ide.Search/GetXCuratedTrends',
                request_serializer=prod__search__pb2.GetXCuratedTrendsRequest.SerializeToString,
                response_deserializer=prod__search__pb2.GetXCuratedTrendsResponse.FromString,
                )
        self.GetXTrendById = channel.unary_unary(
                '/prompt_ide.Search/GetXTrendById',
                request_serializer=prod__search__pb2.GetXTrendByIdRequest.SerializeToString,
                response_deserializer=prod__search__pb2.GetXTrendByIdResponse.FromString,
                )


class SearchServicer(object):
    """Exposes a simple search API.
    """

    def Search(self, request, context):
        """Performs a search with answer summarization using an LLM.
        SearchResponse messages are streamed to the client as they become available. Multiple SearchResponse messages
        of the same type may be sent, with the invariant that each represents a complete snapshot of the data available
        as of the time it was sent. Earlier messages may contain missing fields that are populated in later messages.
        The final SearchResponse message for each subtype will represent the complete state of the data.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMostRecentSearchQuery(self, request, context):
        """Retrieves the most recent search by the current user.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSearchQuery(self, request, context):
        """Deletes search query from storage.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CompleteQuery(self, request, context):
        """Proposes a search query based on a prefix entered by the user.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SummarizeWebResult(self, request, context):
        """Summarizes a website based on a query.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetXCuratedTrends(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetXTrendById(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SearchServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Search': grpc.unary_stream_rpc_method_handler(
                    servicer.Search,
                    request_deserializer=prod__search__pb2.SearchRequest.FromString,
                    response_serializer=prod__search__pb2.SearchResponse.SerializeToString,
            ),
            'GetMostRecentSearchQuery': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMostRecentSearchQuery,
                    request_deserializer=prod__search__pb2.GetMostRecentSearchQueryRequest.FromString,
                    response_serializer=prod__search__pb2.SearchQueryResponse.SerializeToString,
            ),
            'DeleteSearchQuery': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSearchQuery,
                    request_deserializer=prod__search__pb2.DeleteSearchQueryRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'CompleteQuery': grpc.unary_unary_rpc_method_handler(
                    servicer.CompleteQuery,
                    request_deserializer=prod__search__pb2.CompleteQueryRequest.FromString,
                    response_serializer=prod__search__pb2.CompleteQueryResponse.SerializeToString,
            ),
            'SummarizeWebResult': grpc.unary_stream_rpc_method_handler(
                    servicer.SummarizeWebResult,
                    request_deserializer=prod__search__pb2.SummarizeWebResultRequest.FromString,
                    response_serializer=prod__search__pb2.SummarizeWebResultResponse.SerializeToString,
            ),
            'GetXCuratedTrends': grpc.unary_unary_rpc_method_handler(
                    servicer.GetXCuratedTrends,
                    request_deserializer=prod__search__pb2.GetXCuratedTrendsRequest.FromString,
                    response_serializer=prod__search__pb2.GetXCuratedTrendsResponse.SerializeToString,
            ),
            'GetXTrendById': grpc.unary_unary_rpc_method_handler(
                    servicer.GetXTrendById,
                    request_deserializer=prod__search__pb2.GetXTrendByIdRequest.FromString,
                    response_serializer=prod__search__pb2.GetXTrendByIdResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'prompt_ide.Search', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Search(object):
    """Exposes a simple search API.
    """

    @staticmethod
    def Search(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/prompt_ide.Search/Search',
            prod__search__pb2.SearchRequest.SerializeToString,
            prod__search__pb2.SearchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMostRecentSearchQuery(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Search/GetMostRecentSearchQuery',
            prod__search__pb2.GetMostRecentSearchQueryRequest.SerializeToString,
            prod__search__pb2.SearchQueryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteSearchQuery(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Search/DeleteSearchQuery',
            prod__search__pb2.DeleteSearchQueryRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CompleteQuery(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Search/CompleteQuery',
            prod__search__pb2.CompleteQueryRequest.SerializeToString,
            prod__search__pb2.CompleteQueryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SummarizeWebResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/prompt_ide.Search/SummarizeWebResult',
            prod__search__pb2.SummarizeWebResultRequest.SerializeToString,
            prod__search__pb2.SummarizeWebResultResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetXCuratedTrends(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Search/GetXCuratedTrends',
            prod__search__pb2.GetXCuratedTrendsRequest.SerializeToString,
            prod__search__pb2.GetXCuratedTrendsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetXTrendById(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Search/GetXTrendById',
            prod__search__pb2.GetXTrendByIdRequest.SerializeToString,
            prod__search__pb2.GetXTrendByIdResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
