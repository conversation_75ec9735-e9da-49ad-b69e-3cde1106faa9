# fmt: off
# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: chat.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import image_pb2 as image__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nchat.proto\x12\x07xai_api\x1a\x0bimage.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd7\x04\n\x15GetCompletionsRequest\x12\"\n\x08messages\x18\x01 \x03(\x0b\x32\x10.xai_api.Message\x12\r\n\x05model\x18\x02 \x01(\t\x12\x0c\n\x04user\x18\x10 \x01(\t\x12\x0e\n\x01n\x18\x08 \x01(\x05H\x00\x88\x01\x01\x12\x17\n\nmax_tokens\x18\x07 \x01(\x05H\x01\x88\x01\x01\x12\x11\n\x04seed\x18\x0b \x01(\x05H\x02\x88\x01\x01\x12\x0c\n\x04stop\x18\x0c \x03(\t\x12\x18\n\x0btemperature\x18\x0e \x01(\x02H\x03\x88\x01\x01\x12\x12\n\x05top_p\x18\x0f \x01(\x02H\x04\x88\x01\x01\x12\x1e\n\x11\x66requency_penalty\x18\x03 \x01(\x02H\x05\x88\x01\x01\x12\x41\n\nlogit_bias\x18\x04 \x03(\x0b\x32-.xai_api.GetCompletionsRequest.LogitBiasEntry\x12\x10\n\x08logprobs\x18\x05 \x01(\x08\x12\x1d\n\x10presence_penalty\x18\t \x01(\x02H\x06\x88\x01\x01\x12\x30\n\x0fresponse_format\x18\n \x01(\x0b\x32\x17.xai_api.ResponseFormat\x12\x19\n\x0ctop_logprobs\x18\x06 \x01(\x05H\x07\x88\x01\x01\x1a\x30\n\x0eLogitBiasEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x42\x04\n\x02_nB\r\n\x0b_max_tokensB\x07\n\x05_seedB\x0e\n\x0c_temperatureB\x08\n\x06_top_pB\x14\n\x12_frequency_penaltyB\x13\n\x11_presence_penaltyB\x0f\n\r_top_logprobs\"\xc2\x01\n\x16GetChatCompletionChunk\x12\n\n\x02id\x18\x01 \x01(\t\x12%\n\x07\x63hoices\x18\x02 \x03(\x0b\x32\x14.xai_api.ChoiceChunk\x12+\n\x07\x63reated\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05model\x18\x04 \x01(\t\x12\x1a\n\x12system_fingerprint\x18\x05 \x01(\t\x12\x1d\n\x05usage\x18\x06 \x01(\x0b\x32\x0e.xai_api.Usage\"\xc0\x01\n\x19GetChatCompletionResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12 \n\x07\x63hoices\x18\x02 \x03(\x0b\x32\x0f.xai_api.Choice\x12+\n\x07\x63reated\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05model\x18\x06 \x01(\t\x12\x1a\n\x12system_fingerprint\x18\x07 \x01(\t\x12\x1d\n\x05usage\x18\t \x01(\x0b\x32\x0e.xai_api.Usage\"\x8e\x01\n\x0b\x43hoiceChunk\x12\x1d\n\x05\x64\x65lta\x18\x01 \x01(\x0b\x32\x0e.xai_api.Delta\x12#\n\x08logprobs\x18\x02 \x01(\x0b\x32\x11.xai_api.LogProbs\x12,\n\rfinish_reason\x18\x03 \x01(\x0e\x32\x15.xai_api.FinishReason\x12\r\n\x05index\x18\x04 \x01(\x05\"<\n\x05\x44\x65lta\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\"\n\x04role\x18\x02 \x01(\x0e\x32\x14.xai_api.MessageRole\"\x97\x01\n\x06\x43hoice\x12,\n\rfinish_reason\x18\x01 \x01(\x0e\x32\x15.xai_api.FinishReason\x12\r\n\x05index\x18\x02 \x01(\x05\x12+\n\x07message\x18\x03 \x01(\x0b\x32\x1a.xai_api.CompletionMessage\x12#\n\x08logprobs\x18\x04 \x01(\x0b\x32\x11.xai_api.LogProbs\"H\n\x11\x43ompletionMessage\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\"\n\x04role\x18\x02 \x01(\x0e\x32\x14.xai_api.MessageRole\"-\n\x08LogProbs\x12!\n\x07\x63ontent\x18\x01 \x03(\x0b\x32\x10.xai_api.LogProb\"c\n\x07LogProb\x12\r\n\x05token\x18\x01 \x01(\t\x12\x0f\n\x07logprob\x18\x02 \x01(\x02\x12\r\n\x05\x62ytes\x18\x03 \x01(\x0c\x12)\n\x0ctop_logprobs\x18\x04 \x03(\x0b\x32\x13.xai_api.TopLogProb\"\x88\x01\n\x05Usage\x12\x19\n\x11\x63ompletion_tokens\x18\x01 \x01(\x05\x12\x15\n\rprompt_tokens\x18\x02 \x01(\x05\x12\x14\n\x0ctotal_tokens\x18\x03 \x01(\x05\x12\x1a\n\x12prompt_text_tokens\x18\x04 \x01(\x05\x12\x1b\n\x13prompt_image_tokens\x18\x05 \x01(\x05\";\n\nTopLogProb\x12\r\n\x05token\x18\x01 \x01(\t\x12\x0f\n\x07logprob\x18\x02 \x01(\x02\x12\r\n\x05\x62ytes\x18\x03 \x01(\x0c\"\x1e\n\x0eResponseFormat\x12\x0c\n\x04type\x18\x01 \x01(\t\"S\n\x07\x43ontent\x12\x0e\n\x04text\x18\x01 \x01(\tH\x00\x12-\n\timage_url\x18\x02 \x01(\x0b\x32\x18.xai_api.ImageUrlContentH\x00\x42\t\n\x07\x63ontent\"^\n\x07Message\x12!\n\x07\x63ontent\x18\x01 \x03(\x0b\x32\x10.xai_api.Content\x12\"\n\x04role\x18\x02 \x01(\x0e\x32\x14.xai_api.MessageRole\x12\x0c\n\x04name\x18\x03 \x01(\t*f\n\x0bMessageRole\x12\x10\n\x0cINVALID_ROLE\x10\x00\x12\r\n\tROLE_USER\x10\x01\x12\x12\n\x0eROLE_ASSISTANT\x10\x02\x12\x0f\n\x0bROLE_SYSTEM\x10\x03\x12\x11\n\rROLE_FUNCTION\x10\x04*_\n\x0c\x46inishReason\x12\x12\n\x0eREASON_INVALID\x10\x00\x12\x12\n\x0eREASON_MAX_LEN\x10\x01\x12\x16\n\x12REASON_MAX_CONTEXT\x10\x02\x12\x0f\n\x0bREASON_STOP\x10\x03\x32\xb8\x01\n\x04\x43hat\x12U\n\rGetCompletion\x12\x1e.xai_api.GetCompletionsRequest\x1a\".xai_api.GetChatCompletionResponse\"\x00\x12Y\n\x12GetCompletionChunk\x12\x1e.xai_api.GetCompletionsRequest\x1a\x1f.xai_api.GetChatCompletionChunk\"\x00\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'chat_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _GETCOMPLETIONSREQUEST_LOGITBIASENTRY._options = None
  _GETCOMPLETIONSREQUEST_LOGITBIASENTRY._serialized_options = b'8\001'
  _globals['_MESSAGEROLE']._serialized_start=2059
  _globals['_MESSAGEROLE']._serialized_end=2161
  _globals['_FINISHREASON']._serialized_start=2163
  _globals['_FINISHREASON']._serialized_end=2258
  _globals['_GETCOMPLETIONSREQUEST']._serialized_start=70
  _globals['_GETCOMPLETIONSREQUEST']._serialized_end=669
  _globals['_GETCOMPLETIONSREQUEST_LOGITBIASENTRY']._serialized_start=505
  _globals['_GETCOMPLETIONSREQUEST_LOGITBIASENTRY']._serialized_end=553
  _globals['_GETCHATCOMPLETIONCHUNK']._serialized_start=672
  _globals['_GETCHATCOMPLETIONCHUNK']._serialized_end=866
  _globals['_GETCHATCOMPLETIONRESPONSE']._serialized_start=869
  _globals['_GETCHATCOMPLETIONRESPONSE']._serialized_end=1061
  _globals['_CHOICECHUNK']._serialized_start=1064
  _globals['_CHOICECHUNK']._serialized_end=1206
  _globals['_DELTA']._serialized_start=1208
  _globals['_DELTA']._serialized_end=1268
  _globals['_CHOICE']._serialized_start=1271
  _globals['_CHOICE']._serialized_end=1422
  _globals['_COMPLETIONMESSAGE']._serialized_start=1424
  _globals['_COMPLETIONMESSAGE']._serialized_end=1496
  _globals['_LOGPROBS']._serialized_start=1498
  _globals['_LOGPROBS']._serialized_end=1543
  _globals['_LOGPROB']._serialized_start=1545
  _globals['_LOGPROB']._serialized_end=1644
  _globals['_USAGE']._serialized_start=1647
  _globals['_USAGE']._serialized_end=1783
  _globals['_TOPLOGPROB']._serialized_start=1785
  _globals['_TOPLOGPROB']._serialized_end=1844
  _globals['_RESPONSEFORMAT']._serialized_start=1846
  _globals['_RESPONSEFORMAT']._serialized_end=1876
  _globals['_CONTENT']._serialized_start=1878
  _globals['_CONTENT']._serialized_end=1961
  _globals['_MESSAGE']._serialized_start=1963
  _globals['_MESSAGE']._serialized_end=2057
  _globals['_CHAT']._serialized_start=2261
  _globals['_CHAT']._serialized_end=2445
# @@protoc_insertion_point(module_scope)
