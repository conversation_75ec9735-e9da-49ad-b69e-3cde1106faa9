# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import embedder_public_pb2 as embedder__public__pb2


class EmbedderStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Embed = channel.unary_unary(
                '/prompt_ide.Embedder/Embed',
                request_serializer=embedder__public__pb2.EmbedRequest.SerializeToString,
                response_deserializer=embedder__public__pb2.EmbedResponse.FromString,
                )


class EmbedderServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Embed(self, request, context):
        """Embeds the input texts using the specified model.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EmbedderServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Embed': grpc.unary_unary_rpc_method_handler(
                    servicer.Embed,
                    request_deserializer=embedder__public__pb2.EmbedRequest.FromString,
                    response_serializer=embedder__public__pb2.EmbedResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'prompt_ide.Embedder', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Embedder(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Embed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Embedder/Embed',
            embedder__public__pb2.EmbedRequest.SerializeToString,
            embedder__public__pb2.EmbedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
