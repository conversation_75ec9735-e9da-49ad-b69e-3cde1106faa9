# fmt: off
# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: models.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cmodels.proto\x12\x07xai_api\x1a\x1bgoogle/protobuf/empty.proto\"\'\n\x17GetLanguageModelRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"(\n\x18GetEmbeddingModelRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xf1\x01\n\rLanguageModel\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12+\n\x10input_modalities\x18\x03 \x03(\x0e\x32\x11.xai_api.Modality\x12,\n\x11output_modalities\x18\x04 \x03(\x0e\x32\x11.xai_api.Modality\x12\x1f\n\x17prompt_text_token_price\x18\x05 \x01(\x03\x12 \n\x18prompt_image_token_price\x18\x06 \x01(\x03\x12#\n\x1b\x63ompletion_text_token_price\x18\x07 \x01(\x03\"D\n\x1aListLanguageModelsResponse\x12&\n\x06models\x18\x01 \x03(\x0b\x32\x16.xai_api.LanguageModel\"\x9f\x01\n\x0e\x45mbeddingModel\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12+\n\x10input_modalities\x18\x03 \x03(\x0e\x32\x11.xai_api.Modality\x12\x1f\n\x17prompt_text_token_price\x18\x05 \x01(\x03\x12 \n\x18prompt_image_token_price\x18\x06 \x01(\x03\"F\n\x1bListEmbeddingModelsResponse\x12\'\n\x06models\x18\x01 \x03(\x0b\x32\x17.xai_api.EmbeddingModel*,\n\x08Modality\x12\x0b\n\x07INVALID\x10\x00\x12\x08\n\x04TEXT\x10\x01\x12\t\n\x05IMAGE\x10\x02\x32\xd7\x02\n\x06Models\x12S\n\x12ListLanguageModels\x12\x16.google.protobuf.Empty\x1a#.xai_api.ListLanguageModelsResponse\"\x00\x12U\n\x13ListEmbeddingModels\x12\x16.google.protobuf.Empty\x1a$.xai_api.ListEmbeddingModelsResponse\"\x00\x12N\n\x10GetLanguageModel\x12 .xai_api.GetLanguageModelRequest\x1a\x16.xai_api.LanguageModel\"\x00\x12Q\n\x11GetEmbeddingModel\x12!.xai_api.GetEmbeddingModelRequest\x1a\x17.xai_api.EmbeddingModel\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'models_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_MODALITY']._serialized_start=685
  _globals['_MODALITY']._serialized_end=729
  _globals['_GETLANGUAGEMODELREQUEST']._serialized_start=54
  _globals['_GETLANGUAGEMODELREQUEST']._serialized_end=93
  _globals['_GETEMBEDDINGMODELREQUEST']._serialized_start=95
  _globals['_GETEMBEDDINGMODELREQUEST']._serialized_end=135
  _globals['_LANGUAGEMODEL']._serialized_start=138
  _globals['_LANGUAGEMODEL']._serialized_end=379
  _globals['_LISTLANGUAGEMODELSRESPONSE']._serialized_start=381
  _globals['_LISTLANGUAGEMODELSRESPONSE']._serialized_end=449
  _globals['_EMBEDDINGMODEL']._serialized_start=452
  _globals['_EMBEDDINGMODEL']._serialized_end=611
  _globals['_LISTEMBEDDINGMODELSRESPONSE']._serialized_start=613
  _globals['_LISTEMBEDDINGMODELSRESPONSE']._serialized_end=683
  _globals['_MODELS']._serialized_start=732
  _globals['_MODELS']._serialized_end=1075
# @@protoc_insertion_point(module_scope)
