Metadata-Version: 2.1
Name: xai-sdk
Version: 0.3.0
Project-URL: Documentation, https://x.ai/api/sdk.html
Author-email: <PERSON> <poh<PERSON>@x.ai>
License-Expression: MIT
License-File: LICENSE.txt
Classifier: Development Status :: 4 - Beta
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.7
Requires-Dist: deprecated
Requires-Dist: grpcio
Requires-Dist: parameterized
Requires-Dist: protobuf
Description-Content-Type: text/markdown

# xAI SDK



## Installation

```shell
pip install xai-sdk
```

## License

`xai-sdk` is distributed under the terms of the [MIT](https://spdx.org/licenses/MIT.html) license.
