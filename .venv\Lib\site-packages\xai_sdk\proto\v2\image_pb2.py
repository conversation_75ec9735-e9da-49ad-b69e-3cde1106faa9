# fmt: off
# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: image.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bimage.proto\x12\x07xai_api\"J\n\x0fImageUrlContent\x12\x11\n\timage_url\x18\x01 \x01(\t\x12$\n\x06\x64\x65tail\x18\x02 \x01(\x0e\x32\x14.xai_api.ImageDetail*S\n\x0bImageDetail\x12\x12\n\x0e\x44\x45TAIL_INVALID\x10\x00\x12\x0f\n\x0b\x44\x45TAIL_AUTO\x10\x01\x12\x0e\n\nDETAIL_LOW\x10\x02\x12\x0f\n\x0b\x44\x45TAIL_HIGH\x10\x03\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'image_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_IMAGEDETAIL']._serialized_start=100
  _globals['_IMAGEDETAIL']._serialized_end=183
  _globals['_IMAGEURLCONTENT']._serialized_start=24
  _globals['_IMAGEURLCONTENT']._serialized_end=98
# @@protoc_insertion_point(module_scope)
