# NOTE: QuantConnect imports will only work in QuantConnect's cloud environment
# These imports will show as "not resolved" in local IDE but are correct for QuantConnect
from QuantConnect import *
from QuantConnect.Algorithm import QCAlgorithm
import pandas as pd
from datetime import timedelta

class SMCTradingAlgorithm(QCAlgorithm):
    def Initialize(self):
        # Set backtest dates
        self.set_start_date(2024, 6, 1)  # Start date
        self.set_end_date(2024, 12, 31)  # End date
        self.set_cash(10000)            # Starting cash

        # Add forex pair
        self.symbol = self.add_forex("EURUSD", Resolution.MINUTE, Market.OANDA)
        self.confidence_threshold = 80

        # Initialize variables
        self.last_analysis_time = None
        self.position_size = 0.1  # 10% of portfolio per trade
        self.stop_loss_pct = 0.02  # 2% stop loss
        self.take_profit_pct = 0.04  # 4% take profit

        # Warm up algorithm with historical data
        self.set_warm_up(timedelta(days=5))

    def on_data(self, data):
        """Called every minute with new data."""
        if self.is_warming_up:
            return

        # Only analyze every 15 minutes
        current_time = self.time
        if (self.last_analysis_time is None or
            (current_time - self.last_analysis_time).total_seconds() >= 900):  # 15 minutes = 900 seconds

            self.last_analysis_time = current_time
            self.analyze_smc()

    def analyze_smc(self):
        """Analyze SMC patterns and make trading decisions."""
        try:
            # Get market bias from 4H timeframe
            bias = self.get_directional_bias()

            # Analyze Order Blocks and FVGs
            order_block_signal = self.analyze_order_blocks()
            fvg_signal = self.analyze_fair_value_gaps()

            # Execute trading logic
            self.execute_trading_logic(bias, order_block_signal, fvg_signal)

        except Exception as e:
            self.debug(f"Error in SMC analysis: {str(e)}")

    def get_historical_data(self, lookback_minutes=1500):
        """Get historical data for analysis."""
        try:
            # Get 15-minute bars by requesting minute data and resampling
            history = self.history(self.symbol, lookback_minutes, Resolution.MINUTE)
            if history.empty:
                self.debug("No historical data available")
                return pd.DataFrame()

            # Resample to 15-minute bars
            history_15m = history.resample('15T').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            return history_15m

        except Exception as e:
            self.debug(f"Error fetching historical data: {str(e)}")
            return pd.DataFrame()

    def get_directional_bias(self):
        """Determine market direction from higher timeframe."""
        try:
            # Get 4-hour data by resampling
            history = self.history(self.symbol, 2000, Resolution.MINUTE)  # Get enough data
            if history.empty or len(history) < 240:  # Need at least 4 hours of data
                return "none"

            # Resample to 4-hour bars
            history_4h = history.resample('4H').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()

            if len(history_4h) < 2:
                return "none"

            # Simple BOS detection
            latest = history_4h.iloc[-1]
            previous = history_4h.iloc[-2]

            if latest['close'] > previous['high']:
                return "bullish"
            elif latest['close'] < previous['low']:
                return "bearish"
            else:
                return "none"

        except Exception as e:
            self.debug(f"Error in directional bias: {str(e)}")
            return "none"

    def analyze_order_blocks(self):
        """Analyze Order Blocks on 15-minute timeframe."""
        try:
            data = self.get_historical_data(1500)  # 25 hours of minute data
            if data.empty or len(data) < 10:
                return None

            # Look for Order Block pattern: strong move after consolidation
            for i in range(len(data) - 3, 2, -1):
                current = data.iloc[i]
                next_candle = data.iloc[i + 1]

                # Bullish Order Block: bullish candle followed by strong upward move
                if (current['close'] > current['open'] and  # Bullish candle
                    next_candle['close'] > current['high'] * 1.002):  # 0.2% breakout

                    return {
                        "type": "bullish",
                        "entry_price": current['low'],
                        "stop_loss": current['low'] * 0.998,  # 0.2% below low
                        "take_profit": current['low'] * 1.006,  # 0.6% above low
                        "confidence": 85
                    }

                # Bearish Order Block: bearish candle followed by strong downward move
                elif (current['close'] < current['open'] and  # Bearish candle
                      next_candle['close'] < current['low'] * 0.998):  # 0.2% breakdown

                    return {
                        "type": "bearish",
                        "entry_price": current['high'],
                        "stop_loss": current['high'] * 1.002,  # 0.2% above high
                        "take_profit": current['high'] * 0.994,  # 0.6% below high
                        "confidence": 85
                    }

            return None

        except Exception as e:
            self.debug(f"Error in Order Block analysis: {str(e)}")
            return None

    def analyze_fair_value_gaps(self):
        """Analyze Fair Value Gaps on 15-minute timeframe."""
        try:
            data = self.get_historical_data(1500)  # 25 hours of minute data
            if data.empty or len(data) < 5:
                return None

            # Look for FVG pattern: gap between candles
            for i in range(len(data) - 3, 2, -1):
                candle1 = data.iloc[i]
                candle2 = data.iloc[i + 1]
                candle3 = data.iloc[i + 2]

                # Bullish FVG: gap up
                if (candle1['high'] < candle3['low'] and  # Gap exists
                    candle2['close'] > candle2['open']):   # Middle candle is bullish

                    return {
                        "type": "bullish",
                        "entry_price": candle1['high'],  # Enter at gap low
                        "stop_loss": candle1['high'] * 0.998,  # Stop below gap
                        "take_profit": candle3['low'],  # Target gap fill
                        "confidence": 80
                    }

                # Bearish FVG: gap down
                elif (candle1['low'] > candle3['high'] and  # Gap exists
                      candle2['close'] < candle2['open']):   # Middle candle is bearish

                    return {
                        "type": "bearish",
                        "entry_price": candle1['low'],  # Enter at gap high
                        "stop_loss": candle1['low'] * 1.002,  # Stop above gap
                        "take_profit": candle3['high'],  # Target gap fill
                        "confidence": 80
                    }

            return None

        except Exception as e:
            self.debug(f"Error in FVG analysis: {str(e)}")
            return None

    def execute_trading_logic(self, bias, order_block_signal, fvg_signal):
        """Execute trading decisions based on SMC analysis."""
        try:
            current_price = self.securities["EURUSD"].price

            # Check if we already have a position
            if self.portfolio["EURUSD"].invested:
                return

            # Prioritize Order Block signals over FVG signals
            signal = order_block_signal if order_block_signal else fvg_signal

            if not signal:
                return

            # Only trade if signal aligns with bias (or no bias)
            if bias != "none" and signal["type"] != bias:
                return

            # Calculate position size
            portfolio_value = self.portfolio.total_portfolio_value
            position_value = portfolio_value * self.position_size
            quantity = float(position_value / current_price)

            # Execute trade based on signal type
            if signal["type"] == "bullish":
                # Place buy order
                order_ticket = self.market_order("EURUSD", quantity)
                self.debug(f"LONG: Entry={current_price:.5f}, Quantity={quantity:.2f}, Target SL={signal['stop_loss']:.5f}, TP={signal['take_profit']:.5f}")

            elif signal["type"] == "bearish":
                # Place sell order
                order_ticket = self.market_order("EURUSD", -quantity)
                self.debug(f"SHORT: Entry={current_price:.5f}, Quantity={quantity:.2f}, Target SL={signal['stop_loss']:.5f}, TP={signal['take_profit']:.5f}")

        except Exception as e:
            self.debug(f"Error in trading execution: {str(e)}")