# NOTE: QuantConnect imports will only work in QuantConnect's cloud environment
# These imports will show as "not resolved" in local IDE but are correct for QuantConnect
from QuantConnect import *
from QuantConnect.Algorithm import QCAlgorithm
import pandas as pd
from datetime import timed<PERSON><PERSON>
from QuantConnect import *
from QuantConnect.Algorithm import QCAlgorithm
from QuantConnect.Data import Slice, CustomData
import json
from datetime import datetime

class SMCTradingAlgorithm(QCAlgorithm):
    def Initialize(self):
        # Set backtest dates
        self.set_start_date(2024, 6, 1)  # Start date
        self.set_end_date(2024, 12, 31)  # End date
        self.set_cash(10000)            # Starting cash
        # Add forex pair
        self.symbol = self.AddForex("EURUSD", Resolution.Minute, Market.Oanda).Symbol
        self.timeframe_15m = Resolution.MINUTE * 15 # 15-minute resolution
        self.timeframe_4h = Resolution.HOUR * 4  # 4-hour resolution
        self.confidence_threshold = 80
        self.time
        
        # Schedule 15-minute analysis
        self.Schedule.On(self.DateRules.EveryDay(self.symbol), 
                        self.TimeRules.Every(timedelta(minutes=15)), 
                        self.AnalyzeSMC)

    def OnData(self, data):
        # No real-time data processing needed; handled by scheduled task
        pass

    def fetch_ohlc_data(self, symbol, resolution, lookback=100):
        """Fetch OHLC data using QuantConnect's history."""
        history = self.History(symbol, lookback, resolution)
        if history.empty:
            self.Debug(f"No data for {symbol} with {resolution} resolution")
            return []
        ohlc_data = [
            {
                "timestamp": row.index[0].isoformat(),
                "open": float(row.open),
                "high": float(row.high),
                "low": float(row.low),
                "close": float(row.close),
                "volume": int(row.volume)
            } for row in history.itertuples()
        ]
        self.Debug(f"Fetched {len(ohlc_data)} bars for {symbol}")
        return ohlc_data

    def fetch_support_resistance(self, symbol, resolution):
        """Calculate support/resistance levels (simplified)."""
        ohlc_data = self.fetch_ohlc_data(symbol, resolution, lookback=50)
        if not ohlc_data:
            return {"support": 0, "resistance": 0}
        highs = [candle["high"] for candle in ohlc_data]
        lows = [candle["low"] for candle in ohlc_data]
        return {"support": min(lows), "resistance": max(highs)}

    def fetch_break_of_structure(self, symbol, resolution):
        """Detect Break of Structure (simplified)."""
        ohlc_data = self.fetch_ohlc_data(symbol, resolution, lookback=20)
        if not ohlc_data or len(ohlc_data) < 2:
            self.Debug(f"No sufficient data for BOS on {symbol}")
            return {"type": "none", "level": 0, "timestamp": ""}
        latest_candle = ohlc_data[-1]
        previous_candle = ohlc_data[-2]
        if latest_candle["close"] > previous_candle["high"]:
            return {"type": "bullish", "level": previous_candle["high"], "timestamp": latest_candle["timestamp"]}
        elif latest_candle["close"] < previous_candle["low"]:
            return {"type": "bearish", "level": previous_candle["low"], "timestamp": latest_candle["timestamp"]}
        return {"type": "none", "level": 0, "timestamp": ""}

    def analyze_smc(self):
        """Analyze Order Blocks and FVGs on 15-minute timeframe."""
        bias = self.get_directional_bias()
        ohlc_data = self.fetch_ohlc_data(self.symbol, self.timeframe_15m)
        sr_levels = self.fetch_support_resistance(self.symbol, self.timeframe_15m)
        bos_data = self.fetch_break_of_structure(self.symbol, self.timeframe_4h)

        data = {
            "symbol": self.symbol.Value,
            "timeframe": str(self.timeframe_15m),
            "ohlc_data": json.dumps(ohlc_data),
            "sr_levels": json.dumps(sr_levels),
            "bos_data": json.dumps(bos_data)
        }

        order_block_result = self.analyze_order_block(data) if ohlc_data else {}
        if order_block_result and (bias != "none" and order_block_result.get("type") != bias):
            order_block_result = {}

        fvg_result = self.analyze_fvg(data) if ohlc_data else {}
        if fvg_result and (bias != "none" and fvg_result.get("type") != bias):
            fvg_result = {}

        self.trading_decision_engine(order_block_result, fvg_result)

    def get_directional_bias(self):
        """Determine market direction from 4H timeframe."""
        bos_data = self.fetch_break_of_structure(self.symbol, self.timeframe_4h)
        return bos_data.get("type", "none")

    def analyze_order_block(self, data):
        """Manual analysis for Order Blocks (placeholder)."""
        ohlc_data = json.loads(data["ohlc_data"])
        if not ohlc_data or len(ohlc_data) < 3:
            return {}
        for i in range(len(ohlc_data) - 2, 0, -1):
            candle = ohlc_data[i]
            next_candle = ohlc_data[i + 1]
            if candle["close"] > candle["open"] and next_candle["close"] > candle["high"] * 1.005:  # 0.5% move
                return {
                    "timestamp": candle["timestamp"],
                    "type": "bullish",
                    "price_range": {"high": candle["high"], "low": candle["low"]},
                    "confidence": 85,
                    "action": "Wait for retest to buy"
                }
        return {}

    def analyze_fvg(self, data):
        """Manual analysis for Fair Value Gaps (placeholder)."""
        ohlc_data = json.loads(data["ohlc_data"])
        if not ohlc_data or len(ohlc_data) < 3:
            return {}
        for i in range(len(ohlc_data) - 3, 0, -1):
            candle1 = ohlc_data[i]
            candle2 = ohlc_data[i + 1]
            candle3 = ohlc_data[i + 2]
            if candle2["low"] > candle1["high"] and candle3["low"] > candle2["high"]:  # Bullish FVG
                return {
                    "timestamp": candle2["timestamp"],
                    "type": "bullish",
                    "price_range": {"high": candle1["high"], "low": candle3["low"]},
                    "confidence": 80,
                    "action": "Expect price to fill or target"
                }
            elif candle2["high"] < candle1["low"] and candle3["high"] < candle2["low"]:  # Bearish FVG
                return {
                    "timestamp": candle2["timestamp"],
                    "type": "bearish",
                    "price_range": {"high": candle3["high"], "low": candle1["low"]},
                    "confidence": 80,
                    "action": "Expect price to fill or target"
                }
        return {}

    def trading_decision_engine(self, order_block, fvg):
        """Generate trading signals."""
        bias = self.get_directional_bias()
        if order_block and order_block.get("confidence", 0) > self.confidence_threshold and (bias == "none" or order_block.get("type") == bias):
            signal = f"Symbol: {self.symbol.Value}, Type: {order_block['type']}, Entry: {order_block['price_range']['low' if order_block['type'] == 'bullish' else 'high']:.5f}, Action: {order_block['action']}, Confidence: {order_block['confidence']}%, Timestamp: {order_block['timestamp']}, Signal: Order Block"
            self.Debug(signal)
        if fvg and fvg.get("confidence", 0) > self.confidence_threshold and (bias == "none" or fvg.get("type") == bias):
            signal = f"Symbol: {self.symbol.Value}, Type: {fvg['type']}, Target: {fvg['price_range']['low' if fvg['type'] == 'bullish' else 'high']:.5f}, Action: {fvg['action']}, Confidence: {fvg['confidence']}%, Timestamp: {fvg['timestamp']}, Signal: FVG"
            self.Debug(signal)
class SMCTradingAlgorithm(QCAlgorithm):
    def Initialize(self):
        # Set backtest dates
        self.SetStartDate(2024, 6, 1)  # Start date
        self.SetEndDate(2024, 12, 31)  # End date
        self.SetCash(10000)            # Starting cash

        # Add forex pair
        self.symbol = self.AddForex("EURUSD", Resolution.Minute, Market.Oanda).Symbol
        self.confidence_threshold = 80

        # Initialize variables
        self.last_analysis_time = None
        self.position_size = 0.1  # 10% of portfolio per trade
        self.stop_loss_pct = 0.02  # 2% stop loss
        self.take_profit_pct = 0.04  # 4% take profit

        # Warm up algorithm with historical data
        self.SetWarmUp(timedelta(days=5))

    def OnData(self, data):
        """Called every minute with new data."""
        if self.IsWarmingUp:
            return

        # Only analyze every 15 minutes
        current_time = self.Time
        if (self.last_analysis_time is None or
            (current_time - self.last_analysis_time).total_seconds() >= 900):  # 15 minutes = 900 seconds

            self.last_analysis_time = current_time
            self.AnalyzeSMC()

    def AnalyzeSMC(self):
        """Analyze SMC patterns and make trading decisions."""
        try:
            # Get market bias from 4H timeframe
            bias = self.get_directional_bias()

            # Analyze Order Blocks and FVGs
            order_block_signal = self.analyze_order_blocks()
            fvg_signal = self.analyze_fair_value_gaps()

            # Execute trading logic
            self.execute_trading_logic(bias, order_block_signal, fvg_signal)

        except Exception as e:
            self.Debug(f"Error in SMC analysis: {str(e)}")

    def get_historical_data(self, lookback_minutes=1500):
        """Get historical data for analysis."""
        try:
            # Get 15-minute bars by requesting minute data and resampling
            history = self.History(self.symbol, lookback_minutes, Resolution.Minute)
            if history.empty:
                self.Debug("No historical data available")
                return pd.DataFrame()

            # Resample to 15-minute bars
            history_15m = history.resample('15T').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            return history_15m

        except Exception as e:
            self.Debug(f"Error fetching historical data: {str(e)}")
            return pd.DataFrame()

    def get_directional_bias(self):
        """Determine market direction from higher timeframe."""
        try:
            # Get 4-hour data by resampling
            history = self.History(self.symbol, 2000, Resolution.Minute)  # Get enough data
            if history.empty or len(history) < 240:  # Need at least 4 hours of data
                return "none"

            # Resample to 4-hour bars
            history_4h = history.resample('4H').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()

            if len(history_4h) < 2:
                return "none"

            # Simple BOS detection
            latest = history_4h.iloc[-1]
            previous = history_4h.iloc[-2]

            if latest['close'] > previous['high']:
                return "bullish"
            elif latest['close'] < previous['low']:
                return "bearish"
            else:
                return "none"

        except Exception as e:
            self.Debug(f"Error in directional bias: {str(e)}")
            return "none"

    def analyze_order_blocks(self):
        """Analyze Order Blocks on 15-minute timeframe."""
        try:
            data = self.get_historical_data(1500)  # 25 hours of minute data
            if data.empty or len(data) < 10:
                return None

            # Look for Order Block pattern: strong move after consolidation
            for i in range(len(data) - 3, 2, -1):
                current = data.iloc[i]
                next_candle = data.iloc[i + 1]

                # Bullish Order Block: bullish candle followed by strong upward move
                if (current['close'] > current['open'] and  # Bullish candle
                    next_candle['close'] > current['high'] * 1.002):  # 0.2% breakout

                    return {
                        "type": "bullish",
                        "entry_price": current['low'],
                        "stop_loss": current['low'] * 0.998,  # 0.2% below low
                        "take_profit": current['low'] * 1.006,  # 0.6% above low
                        "confidence": 85
                    }

                # Bearish Order Block: bearish candle followed by strong downward move
                elif (current['close'] < current['open'] and  # Bearish candle
                      next_candle['close'] < current['low'] * 0.998):  # 0.2% breakdown

                    return {
                        "type": "bearish",
                        "entry_price": current['high'],
                        "stop_loss": current['high'] * 1.002,  # 0.2% above high
                        "take_profit": current['high'] * 0.994,  # 0.6% below high
                        "confidence": 85
                    }

            return None

        except Exception as e:
            self.Debug(f"Error in Order Block analysis: {str(e)}")
            return None

    def analyze_fair_value_gaps(self):
        """Analyze Fair Value Gaps on 15-minute timeframe."""
        try:
            data = self.get_historical_data(1500)  # 25 hours of minute data
            if data.empty or len(data) < 5:
                return None

            # Look for FVG pattern: gap between candles
            for i in range(len(data) - 3, 2, -1):
                candle1 = data.iloc[i]
                candle2 = data.iloc[i + 1]
                candle3 = data.iloc[i + 2]

                # Bullish FVG: gap up
                if (candle1['high'] < candle3['low'] and  # Gap exists
                    candle2['close'] > candle2['open']):   # Middle candle is bullish

                    return {
                        "type": "bullish",
                        "entry_price": candle1['high'],  # Enter at gap low
                        "stop_loss": candle1['high'] * 0.998,  # Stop below gap
                        "take_profit": candle3['low'],  # Target gap fill
                        "confidence": 80
                    }

                # Bearish FVG: gap down
                elif (candle1['low'] > candle3['high'] and  # Gap exists
                      candle2['close'] < candle2['open']):   # Middle candle is bearish

                    return {
                        "type": "bearish",
                        "entry_price": candle1['low'],  # Enter at gap high
                        "stop_loss": candle1['low'] * 1.002,  # Stop above gap
                        "take_profit": candle3['high'],  # Target gap fill
                        "confidence": 80
                    }

            return None

        except Exception as e:
            self.Debug(f"Error in FVG analysis: {str(e)}")
            return None

    def execute_trading_logic(self, bias, order_block_signal, fvg_signal):
        """Execute trading decisions based on SMC analysis."""
        try:
            current_price = self.Securities[self.symbol].Price

            # Check if we already have a position
            if self.Portfolio[self.symbol].Invested:
                return

            # Prioritize Order Block signals over FVG signals
            signal = order_block_signal if order_block_signal else fvg_signal

            if not signal:
                return

            # Only trade if signal aligns with bias (or no bias)
            if bias != "none" and signal["type"] != bias:
                return

            # Calculate position size
            portfolio_value = self.Portfolio.TotalPortfolioValue
            position_value = portfolio_value * self.position_size
            quantity = position_value / current_price

            # Execute trade based on signal type
            if signal["type"] == "bullish":
                # Place buy order with stop loss and take profit
                self.MarketOrder(self.symbol, quantity)
                self.StopMarketOrder(self.symbol, -quantity, signal["stop_loss"])
                self.LimitOrder(self.symbol, -quantity, signal["take_profit"])

                self.Debug(f"LONG: Entry={current_price:.5f}, SL={signal['stop_loss']:.5f}, TP={signal['take_profit']:.5f}")

            elif signal["type"] == "bearish":
                # Place sell order with stop loss and take profit
                self.MarketOrder(self.symbol, -quantity)
                self.StopMarketOrder(self.symbol, quantity, signal["stop_loss"])
                self.LimitOrder(self.symbol, quantity, signal["take_profit"])

                self.Debug(f"SHORT: Entry={current_price:.5f}, SL={signal['stop_loss']:.5f}, TP={signal['take_profit']:.5f}")

        except Exception as e:
            self.Debug(f"Error in trading execution: {str(e)}")