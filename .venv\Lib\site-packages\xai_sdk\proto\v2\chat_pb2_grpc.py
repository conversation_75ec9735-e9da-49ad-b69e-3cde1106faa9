# fmt: off
# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import chat_pb2 as chat__pb2


class ChatStub(object):
    """An API that exposes our language models via a Chat interface. The API design is loosely based on
    offerings by our competitors.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCompletion = channel.unary_unary(
                '/xai_api.Chat/GetCompletion',
                request_serializer=chat__pb2.GetCompletionsRequest.SerializeToString,
                response_deserializer=chat__pb2.GetChatCompletionResponse.FromString,
                )
        self.GetCompletionChunk = channel.unary_stream(
                '/xai_api.Chat/GetCompletionChunk',
                request_serializer=chat__pb2.GetCompletionsRequest.SerializeToString,
                response_deserializer=chat__pb2.GetChatCompletionChunk.FromString,
                )


class ChatServicer(object):
    """An API that exposes our language models via a Chat interface. The API design is loosely based on
    offerings by our competitors.
    """

    def GetCompletion(self, request, context):
        """Samples a response from the model and blocks until the response has been fully generated.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCompletionChunk(self, request, context):
        """Samples a response from the model and streams out the model tokens as they are being generated.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ChatServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCompletion': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCompletion,
                    request_deserializer=chat__pb2.GetCompletionsRequest.FromString,
                    response_serializer=chat__pb2.GetChatCompletionResponse.SerializeToString,
            ),
            'GetCompletionChunk': grpc.unary_stream_rpc_method_handler(
                    servicer.GetCompletionChunk,
                    request_deserializer=chat__pb2.GetCompletionsRequest.FromString,
                    response_serializer=chat__pb2.GetChatCompletionChunk.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'xai_api.Chat', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Chat(object):
    """An API that exposes our language models via a Chat interface. The API design is loosely based on
    offerings by our competitors.
    """

    @staticmethod
    def GetCompletion(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/xai_api.Chat/GetCompletion',
            chat__pb2.GetCompletionsRequest.SerializeToString,
            chat__pb2.GetChatCompletionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCompletionChunk(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/xai_api.Chat/GetCompletionChunk',
            chat__pb2.GetCompletionsRequest.SerializeToString,
            chat__pb2.GetChatCompletionChunk.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
