# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from . import stateless_chat_pb2 as stateless__chat__pb2


class StatelessChatStub(object):
    """This is a simplified interface for interacting with Grok. It's entirely stateless but exposes
    less information than the full implementation in chat.proto. It's main purpose is to power the
    Grok-on-X experience.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.AddResponse = channel.unary_stream(
                '/prompt_ide.StatelessChat/AddResponse',
                request_serializer=stateless__chat__pb2.StatelessConversation.SerializeToString,
                response_deserializer=stateless__chat__pb2.StatelessResponse.FromString,
                )
        self.LogForDebugging = channel.unary_unary(
                '/prompt_ide.StatelessChat/LogForDebugging',
                request_serializer=stateless__chat__pb2.StatelessConversation.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.DeleteLoggedConversations = channel.unary_unary(
                '/prompt_ide.StatelessChat/DeleteLoggedConversations',
                request_serializer=stateless__chat__pb2.DeleteLoggedConversationsRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )


class StatelessChatServicer(object):
    """This is a simplified interface for interacting with Grok. It's entirely stateless but exposes
    less information than the full implementation in chat.proto. It's main purpose is to power the
    Grok-on-X experience.
    """

    def AddResponse(self, request, context):
        """Samples a new response from the model. This service will always call Grok instead of the raw
        model.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LogForDebugging(self, request, context):
        """Adds a response to our debugging database.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteLoggedConversations(self, request, context):
        """Deletes all logged conversations under a given accounting key.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_StatelessChatServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'AddResponse': grpc.unary_stream_rpc_method_handler(
                    servicer.AddResponse,
                    request_deserializer=stateless__chat__pb2.StatelessConversation.FromString,
                    response_serializer=stateless__chat__pb2.StatelessResponse.SerializeToString,
            ),
            'LogForDebugging': grpc.unary_unary_rpc_method_handler(
                    servicer.LogForDebugging,
                    request_deserializer=stateless__chat__pb2.StatelessConversation.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'DeleteLoggedConversations': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteLoggedConversations,
                    request_deserializer=stateless__chat__pb2.DeleteLoggedConversationsRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'prompt_ide.StatelessChat', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class StatelessChat(object):
    """This is a simplified interface for interacting with Grok. It's entirely stateless but exposes
    less information than the full implementation in chat.proto. It's main purpose is to power the
    Grok-on-X experience.
    """

    @staticmethod
    def AddResponse(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/prompt_ide.StatelessChat/AddResponse',
            stateless__chat__pb2.StatelessConversation.SerializeToString,
            stateless__chat__pb2.StatelessResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LogForDebugging(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.StatelessChat/LogForDebugging',
            stateless__chat__pb2.StatelessConversation.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteLoggedConversations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.StatelessChat/DeleteLoggedConversations',
            stateless__chat__pb2.DeleteLoggedConversationsRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
