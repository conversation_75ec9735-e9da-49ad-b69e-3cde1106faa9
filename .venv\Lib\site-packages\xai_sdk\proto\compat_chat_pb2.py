# fmt: off
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: compat_chat.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x63ompat_chat.proto\x12\x0b\x63ompat.chat\"\xc7\x03\n\x15GetCompletionsRequest\x12&\n\x08messages\x18\x01 \x03(\x0b\x32\x14.compat.chat.Message\x12\r\n\x05model\x18\x02 \x01(\t\x12\x19\n\x11\x66requency_penalty\x18\x03 \x01(\x02\x12\x45\n\nlogit_bias\x18\x04 \x03(\x0b\x32\x31.compat.chat.GetCompletionsRequest.LogitBiasEntry\x12\x10\n\x08logprobs\x18\x05 \x01(\x08\x12\x14\n\x0ctop_logprobs\x18\x06 \x01(\x05\x12\x12\n\nmax_tokens\x18\x07 \x01(\x05\x12\t\n\x01n\x18\x08 \x01(\x05\x12\x18\n\x10presence_penalty\x18\t \x01(\x02\x12\x34\n\x0fresponse_format\x18\n \x01(\x0b\x32\x1b.compat.chat.ResponseFormat\x12\x0c\n\x04seed\x18\x0b \x01(\x05\x12\x0c\n\x04stop\x18\x0c \x03(\t\x12\x13\n\x0btemperature\x18\x0e \x01(\x02\x12\r\n\x05top_p\x18\x0f \x01(\x02\x12\x0c\n\x04user\x18\x10 \x01(\t\x1a\x30\n\x0eLogitBiasEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x9b\x01\n\x16GetChatCompletionChunk\x12\n\n\x02id\x18\x01 \x01(\t\x12)\n\x07\x63hoices\x18\x02 \x03(\x0b\x32\x18.compat.chat.ChoiceChunk\x12\x0f\n\x07\x63reated\x18\x03 \x01(\r\x12\r\n\x05model\x18\x04 \x01(\t\x12\x1a\n\x12system_fingerprint\x18\x05 \x01(\t\x12\x0e\n\x06object\x18\x06 \x01(\t\"\xbc\x01\n\x19GetChatCompletionResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12$\n\x07\x63hoices\x18\x02 \x03(\x0b\x32\x13.compat.chat.Choice\x12\x0f\n\x07\x63reated\x18\x05 \x01(\r\x12\r\n\x05model\x18\x06 \x01(\t\x12\x1a\n\x12system_fingerprint\x18\x07 \x01(\t\x12\x0e\n\x06object\x18\x08 \x01(\t\x12!\n\x05usage\x18\t \x01(\x0b\x32\x12.compat.chat.Usage\"\x7f\n\x0b\x43hoiceChunk\x12!\n\x05\x64\x65lta\x18\x01 \x01(\x0b\x32\x12.compat.chat.Delta\x12\'\n\x08logprobs\x18\x02 \x01(\x0b\x32\x15.compat.chat.LogProbs\x12\x15\n\rfinish_reason\x18\x03 \x01(\t\x12\r\n\x05index\x18\x04 \x01(\x05\"&\n\x05\x44\x65lta\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x0c\n\x04role\x18\x02 \x01(\t\"\x88\x01\n\x06\x43hoice\x12\x15\n\rfinish_reason\x18\x01 \x01(\t\x12\r\n\x05index\x18\x02 \x01(\x05\x12/\n\x07message\x18\x03 \x01(\x0b\x32\x1e.compat.chat.CompletionMessage\x12\'\n\x08logprobs\x18\x04 \x01(\x0b\x32\x15.compat.chat.LogProbs\"2\n\x11\x43ompletionMessage\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x0c\n\x04role\x18\x02 \x01(\t\"1\n\x08LogProbs\x12%\n\x07\x63ontent\x18\x01 \x03(\x0b\x32\x14.compat.chat.LogProb\"g\n\x07LogProb\x12\r\n\x05token\x18\x01 \x01(\t\x12\x0f\n\x07logprob\x18\x02 \x01(\x02\x12\r\n\x05\x62ytes\x18\x03 \x01(\x0c\x12-\n\x0ctop_logprobs\x18\x04 \x03(\x0b\x32\x17.compat.chat.TopLogProb\"O\n\x05Usage\x12\x19\n\x11\x63ompletion_tokens\x18\x01 \x01(\x05\x12\x15\n\rprompt_tokens\x18\x02 \x01(\x05\x12\x14\n\x0ctotal_tokens\x18\x03 \x01(\x05\";\n\nTopLogProb\x12\r\n\x05token\x18\x01 \x01(\t\x12\x0f\n\x07logprob\x18\x02 \x01(\x02\x12\r\n\x05\x62ytes\x18\x03 \x01(\x0c\"\x1e\n\x0eResponseFormat\x12\x0c\n\x04type\x18\x01 \x01(\t\"\'\n\x08ImageUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0e\n\x06\x64\x65tail\x18\x02 \x01(\t\"b\n\x07\x43ontent\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0c\n\x04text\x18\x02 \x01(\t\x12-\n\timage_url\x18\x03 \x01(\x0b\x32\x15.compat.chat.ImageUrlH\x00\x88\x01\x01\x42\x0c\n\n_image_url\"h\n\x07Message\x12\x1a\n\x0e\x63ontent_legacy\x18\x01 \x01(\tB\x02\x18\x01\x12%\n\x07\x63ontent\x18\x04 \x03(\x0b\x32\x14.compat.chat.Content\x12\x0c\n\x04role\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t2\xc8\x01\n\x04\x43hat\x12]\n\rGetCompletion\x12\".compat.chat.GetCompletionsRequest\x1a&.compat.chat.GetChatCompletionResponse\"\x00\x12\x61\n\x12GetCompletionChunk\x12\".compat.chat.GetCompletionsRequest\x1a#.compat.chat.GetChatCompletionChunk\"\x00\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'compat_chat_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _GETCOMPLETIONSREQUEST_LOGITBIASENTRY._options = None
  _GETCOMPLETIONSREQUEST_LOGITBIASENTRY._serialized_options = b'8\001'
  _MESSAGE.fields_by_name['content_legacy']._options = None
  _MESSAGE.fields_by_name['content_legacy']._serialized_options = b'\030\001'
  _globals['_GETCOMPLETIONSREQUEST']._serialized_start=35
  _globals['_GETCOMPLETIONSREQUEST']._serialized_end=490
  _globals['_GETCOMPLETIONSREQUEST_LOGITBIASENTRY']._serialized_start=442
  _globals['_GETCOMPLETIONSREQUEST_LOGITBIASENTRY']._serialized_end=490
  _globals['_GETCHATCOMPLETIONCHUNK']._serialized_start=493
  _globals['_GETCHATCOMPLETIONCHUNK']._serialized_end=648
  _globals['_GETCHATCOMPLETIONRESPONSE']._serialized_start=651
  _globals['_GETCHATCOMPLETIONRESPONSE']._serialized_end=839
  _globals['_CHOICECHUNK']._serialized_start=841
  _globals['_CHOICECHUNK']._serialized_end=968
  _globals['_DELTA']._serialized_start=970
  _globals['_DELTA']._serialized_end=1008
  _globals['_CHOICE']._serialized_start=1011
  _globals['_CHOICE']._serialized_end=1147
  _globals['_COMPLETIONMESSAGE']._serialized_start=1149
  _globals['_COMPLETIONMESSAGE']._serialized_end=1199
  _globals['_LOGPROBS']._serialized_start=1201
  _globals['_LOGPROBS']._serialized_end=1250
  _globals['_LOGPROB']._serialized_start=1252
  _globals['_LOGPROB']._serialized_end=1355
  _globals['_USAGE']._serialized_start=1357
  _globals['_USAGE']._serialized_end=1436
  _globals['_TOPLOGPROB']._serialized_start=1438
  _globals['_TOPLOGPROB']._serialized_end=1497
  _globals['_RESPONSEFORMAT']._serialized_start=1499
  _globals['_RESPONSEFORMAT']._serialized_end=1529
  _globals['_IMAGEURL']._serialized_start=1531
  _globals['_IMAGEURL']._serialized_end=1570
  _globals['_CONTENT']._serialized_start=1572
  _globals['_CONTENT']._serialized_end=1670
  _globals['_MESSAGE']._serialized_start=1672
  _globals['_MESSAGE']._serialized_end=1776
  _globals['_CHAT']._serialized_start=1779
  _globals['_CHAT']._serialized_end=1979
# @@protoc_insertion_point(module_scope)
