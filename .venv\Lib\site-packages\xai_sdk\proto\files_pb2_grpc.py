# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import files_pb2 as files__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


class FileStub(object):
    """Service for uploading, downloading, and deleting files. We only allow storing somewhat small
    files using this service (e.g. 5 MiB - check the implementation for details).
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UploadFile = channel.unary_unary(
                '/prompt_ide.File/UploadFile',
                request_serializer=files__pb2.UploadFileRequest.SerializeToString,
                response_deserializer=files__pb2.FileMetadata.FromString,
                )
        self.DownloadFile = channel.unary_unary(
                '/prompt_ide.File/DownloadFile',
                request_serializer=files__pb2.DownloadFileRequest.SerializeToString,
                response_deserializer=files__pb2.FileObject.FromString,
                )
        self.ListFiles = channel.unary_unary(
                '/prompt_ide.File/ListFiles',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=files__pb2.ListFilesResponse.FromString,
                )
        self.RenameFile = channel.unary_unary(
                '/prompt_ide.File/RenameFile',
                request_serializer=files__pb2.RenameFileRequest.SerializeToString,
                response_deserializer=files__pb2.FileMetadata.FromString,
                )
        self.DeleteFile = channel.unary_unary(
                '/prompt_ide.File/DeleteFile',
                request_serializer=files__pb2.DeleteFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )


class FileServicer(object):
    """Service for uploading, downloading, and deleting files. We only allow storing somewhat small
    files using this service (e.g. 5 MiB - check the implementation for details).
    """

    def UploadFile(self, request, context):
        """Uploads a new file.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DownloadFile(self, request, context):
        """Downloads a file.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListFiles(self, request, context):
        """Lists all files.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RenameFile(self, request, context):
        """Renames a file.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteFile(self, request, context):
        """Deletes an individual file.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FileServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UploadFile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadFile,
                    request_deserializer=files__pb2.UploadFileRequest.FromString,
                    response_serializer=files__pb2.FileMetadata.SerializeToString,
            ),
            'DownloadFile': grpc.unary_unary_rpc_method_handler(
                    servicer.DownloadFile,
                    request_deserializer=files__pb2.DownloadFileRequest.FromString,
                    response_serializer=files__pb2.FileObject.SerializeToString,
            ),
            'ListFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.ListFiles,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=files__pb2.ListFilesResponse.SerializeToString,
            ),
            'RenameFile': grpc.unary_unary_rpc_method_handler(
                    servicer.RenameFile,
                    request_deserializer=files__pb2.RenameFileRequest.FromString,
                    response_serializer=files__pb2.FileMetadata.SerializeToString,
            ),
            'DeleteFile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteFile,
                    request_deserializer=files__pb2.DeleteFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'prompt_ide.File', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class File(object):
    """Service for uploading, downloading, and deleting files. We only allow storing somewhat small
    files using this service (e.g. 5 MiB - check the implementation for details).
    """

    @staticmethod
    def UploadFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.File/UploadFile',
            files__pb2.UploadFileRequest.SerializeToString,
            files__pb2.FileMetadata.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DownloadFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.File/DownloadFile',
            files__pb2.DownloadFileRequest.SerializeToString,
            files__pb2.FileObject.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.File/ListFiles',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            files__pb2.ListFilesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RenameFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.File/RenameFile',
            files__pb2.RenameFileRequest.SerializeToString,
            files__pb2.FileMetadata.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.File/DeleteFile',
            files__pb2.DeleteFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
