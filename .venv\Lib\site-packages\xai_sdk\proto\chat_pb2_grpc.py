# fmt: off
# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import chat_pb2 as chat__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


class ChatStub(object):
    """Utility for interacting with a model via a chat-like interface.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateConversation = channel.unary_unary(
                '/prompt_ide.Chat/CreateConversation',
                request_serializer=chat__pb2.CreateConversationRequest.SerializeToString,
                response_deserializer=chat__pb2.Conversation.FromString,
                )
        self.GetConversation = channel.unary_unary(
                '/prompt_ide.Chat/GetConversation',
                request_serializer=chat__pb2.GetConversationRequest.SerializeToString,
                response_deserializer=chat__pb2.Conversation.FromString,
                )
        self.ListConversations = channel.unary_unary(
                '/prompt_ide.Chat/ListConversations',
                request_serializer=chat__pb2.ListConversationsRequest.SerializeToString,
                response_deserializer=chat__pb2.ListConversationsResponse.FromString,
                )
        self.AddResponse = channel.unary_stream(
                '/prompt_ide.Chat/AddResponse',
                request_serializer=chat__pb2.AddResponseRequest.SerializeToString,
                response_deserializer=chat__pb2.AddResponseResponse.FromString,
                )
        self.DeleteConversation = channel.unary_unary(
                '/prompt_ide.Chat/DeleteConversation',
                request_serializer=chat__pb2.DeleteConversationRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.GenerateTitle = channel.unary_unary(
                '/prompt_ide.Chat/GenerateTitle',
                request_serializer=chat__pb2.GenerateTitleRequest.SerializeToString,
                response_deserializer=chat__pb2.GenerateTitleResponse.FromString,
                )
        self.UpdateConversation = channel.unary_unary(
                '/prompt_ide.Chat/UpdateConversation',
                request_serializer=chat__pb2.UpdateConversationRequest.SerializeToString,
                response_deserializer=chat__pb2.Conversation.FromString,
                )
        self.AddModelResponse = channel.unary_unary(
                '/prompt_ide.Chat/AddModelResponse',
                request_serializer=chat__pb2.AddModelResponseRequest.SerializeToString,
                response_deserializer=chat__pb2.Response.FromString,
                )
        self.ShareConversation = channel.unary_unary(
                '/prompt_ide.Chat/ShareConversation',
                request_serializer=chat__pb2.ShareConversationRequest.SerializeToString,
                response_deserializer=chat__pb2.ShareConversationResponse.FromString,
                )
        self.SearchConversations = channel.unary_unary(
                '/prompt_ide.Chat/SearchConversations',
                request_serializer=chat__pb2.SearchConversationsRequest.SerializeToString,
                response_deserializer=chat__pb2.SearchConversationsResponse.FromString,
                )


class ChatServicer(object):
    """Utility for interacting with a model via a chat-like interface.
    """

    def CreateConversation(self, request, context):
        """Starts a new conversation.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConversation(self, request, context):
        """Retrieves an entire conversation.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListConversations(self, request, context):
        """Lists all conversations by the user.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddResponse(self, request, context):
        """Adds a human response to a conversation and streams the response
        token-by-token to the client.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteConversation(self, request, context):
        """Deletes a conversation and all its responses.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateTitle(self, request, context):
        """Creates a new conversation title based on the content of this conversation.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateConversation(self, request, context):
        """Allows the user to set some properties on a conversation.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddModelResponse(self, request, context):
        """Manually creates a model response.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ShareConversation(self, request, context):
        """Shares a branch of a conversation.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchConversations(self, request, context):
        """Search conversation based on a query.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ChatServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateConversation,
                    request_deserializer=chat__pb2.CreateConversationRequest.FromString,
                    response_serializer=chat__pb2.Conversation.SerializeToString,
            ),
            'GetConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConversation,
                    request_deserializer=chat__pb2.GetConversationRequest.FromString,
                    response_serializer=chat__pb2.Conversation.SerializeToString,
            ),
            'ListConversations': grpc.unary_unary_rpc_method_handler(
                    servicer.ListConversations,
                    request_deserializer=chat__pb2.ListConversationsRequest.FromString,
                    response_serializer=chat__pb2.ListConversationsResponse.SerializeToString,
            ),
            'AddResponse': grpc.unary_stream_rpc_method_handler(
                    servicer.AddResponse,
                    request_deserializer=chat__pb2.AddResponseRequest.FromString,
                    response_serializer=chat__pb2.AddResponseResponse.SerializeToString,
            ),
            'DeleteConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteConversation,
                    request_deserializer=chat__pb2.DeleteConversationRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GenerateTitle': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateTitle,
                    request_deserializer=chat__pb2.GenerateTitleRequest.FromString,
                    response_serializer=chat__pb2.GenerateTitleResponse.SerializeToString,
            ),
            'UpdateConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateConversation,
                    request_deserializer=chat__pb2.UpdateConversationRequest.FromString,
                    response_serializer=chat__pb2.Conversation.SerializeToString,
            ),
            'AddModelResponse': grpc.unary_unary_rpc_method_handler(
                    servicer.AddModelResponse,
                    request_deserializer=chat__pb2.AddModelResponseRequest.FromString,
                    response_serializer=chat__pb2.Response.SerializeToString,
            ),
            'ShareConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.ShareConversation,
                    request_deserializer=chat__pb2.ShareConversationRequest.FromString,
                    response_serializer=chat__pb2.ShareConversationResponse.SerializeToString,
            ),
            'SearchConversations': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchConversations,
                    request_deserializer=chat__pb2.SearchConversationsRequest.FromString,
                    response_serializer=chat__pb2.SearchConversationsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'prompt_ide.Chat', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Chat(object):
    """Utility for interacting with a model via a chat-like interface.
    """

    @staticmethod
    def CreateConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/CreateConversation',
            chat__pb2.CreateConversationRequest.SerializeToString,
            chat__pb2.Conversation.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/GetConversation',
            chat__pb2.GetConversationRequest.SerializeToString,
            chat__pb2.Conversation.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListConversations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/ListConversations',
            chat__pb2.ListConversationsRequest.SerializeToString,
            chat__pb2.ListConversationsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddResponse(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/prompt_ide.Chat/AddResponse',
            chat__pb2.AddResponseRequest.SerializeToString,
            chat__pb2.AddResponseResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/DeleteConversation',
            chat__pb2.DeleteConversationRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GenerateTitle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/GenerateTitle',
            chat__pb2.GenerateTitleRequest.SerializeToString,
            chat__pb2.GenerateTitleResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/UpdateConversation',
            chat__pb2.UpdateConversationRequest.SerializeToString,
            chat__pb2.Conversation.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddModelResponse(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/AddModelResponse',
            chat__pb2.AddModelResponseRequest.SerializeToString,
            chat__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ShareConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/ShareConversation',
            chat__pb2.ShareConversationRequest.SerializeToString,
            chat__pb2.ShareConversationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchConversations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/prompt_ide.Chat/SearchConversations',
            chat__pb2.SearchConversationsRequest.SerializeToString,
            chat__pb2.SearchConversationsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
